import { test, expect } from '@playwright/test'

test.describe('Navigation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should navigate to Owner\'s Manual section', async ({ page }) => {
    await page.click('text=Owner\'s Manual')
    await expect(page).toHaveURL('/section/owners-manual')
    await expect(page.locator('h1')).toContainText('Owner\'s Manual')
    await expect(page.locator('text=Basic understanding and navigation')).toBeVisible()
  })

  test('should navigate to Technical Specs section', async ({ page }) => {
    await page.click('text=Technical Specs')
    await expect(page).toHaveURL('/section/technical-specs')
    await expect(page.locator('h1')).toContainText('Technical Specifications')
    await expect(page.locator('text=Deep dive into AI system architecture')).toBeVisible()
  })

  test('should navigate to Operating Instructions section', async ({ page }) => {
    await page.click('text=Operating Instructions')
    await expect(page).toHaveURL('/section/operating-instructions')
    await expect(page.locator('h1')).toContainText('Operating Instructions')
    await expect(page.locator('text=Step-by-step guides')).toBeVisible()
  })

  test('should navigate to Advanced Techniques section', async ({ page }) => {
    await page.click('text=Advanced Techniques')
    await expect(page).toHaveURL('/section/advanced-techniques')
    await expect(page.locator('h1')).toContainText('Advanced Techniques')
    await expect(page.locator('text=Creative applications beyond standard')).toBeVisible()
  })

  test('should show active navigation state', async ({ page }) => {
    // Navigate to a section
    await page.click('text=Owner\'s Manual')
    
    // Check that the navigation item is marked as active
    const activeNavItem = page.locator('[data-nav-item="owners-manual"]')
    await expect(activeNavItem).toHaveAttribute('aria-current', 'page')
    await expect(activeNavItem).toHaveClass(/bg-primary-600/)
  })

  test('should have working navigation progress indicator', async ({ page }) => {
    // Check progress indicator exists
    const progressIndicator = page.locator('.absolute.bottom-0.left-0.right-0.h-0\\.5')
    await expect(progressIndicator).toBeVisible()
    
    // Navigate to different sections and check progress updates
    await page.click('text=Technical Specs')
    await page.waitForTimeout(500)
    
    // Check that progress bar width has changed
    const progressBar = page.locator('.h-full.bg-gradient-to-r.from-primary-500.to-accent-500')
    await expect(progressBar).toBeVisible()
  })

  test('should handle direct URL navigation', async ({ page }) => {
    // Navigate directly to a section URL
    await page.goto('/section/advanced-techniques')
    
    // Check page loads correctly
    await expect(page.locator('h1')).toContainText('Advanced Techniques')
    
    // Check navigation shows correct active state
    const activeNavItem = page.locator('[data-nav-item="advanced-techniques"]')
    await expect(activeNavItem).toHaveAttribute('aria-current', 'page')
  })

  test('should handle invalid URLs', async ({ page }) => {
    // Navigate to invalid section
    await page.goto('/section/invalid-section')
    
    // Should redirect to home page
    await expect(page).toHaveURL('/')
    await expect(page.locator('h1')).toContainText('AI System Prompts')
  })

  test('should have working header navigation', async ({ page }) => {
    // Test logo link
    await page.click('text=AI Prompts Mastery')
    await expect(page).toHaveURL('/')
    
    // Navigate to a section first
    await page.click('text=Technical Specs')
    await expect(page).toHaveURL('/section/technical-specs')
    
    // Click logo again to return home
    await page.click('text=AI Prompts Mastery')
    await expect(page).toHaveURL('/')
  })

  test('should have working dropdown navigation on desktop', async ({ page }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 })
    
    // Hover over "More" dropdown
    const moreButton = page.locator('text=More').first()
    await moreButton.hover()
    
    // Check dropdown items are visible
    await expect(page.locator('text=Troubleshooting')).toBeVisible()
    await expect(page.locator('text=Maintenance')).toBeVisible()
    await expect(page.locator('text=Safety & Compliance')).toBeVisible()
    
    // Click on a dropdown item
    await page.click('text=Troubleshooting')
    await expect(page).toHaveURL('/section/troubleshooting')
  })

  test('should maintain scroll position on navigation', async ({ page }) => {
    // Scroll down on homepage
    await page.evaluate(() => window.scrollTo(0, 500))
    const initialScrollY = await page.evaluate(() => window.scrollY)
    expect(initialScrollY).toBeGreaterThan(0)
    
    // Navigate to another section
    await page.click('text=Owner\'s Manual')
    await expect(page).toHaveURL('/section/owners-manual')
    
    // Check that we're at the top of the new page
    const newScrollY = await page.evaluate(() => window.scrollY)
    expect(newScrollY).toBeLessThan(100) // Allow for some header offset
  })

  test('should have keyboard navigation support', async ({ page }) => {
    // Focus on navigation
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab') // Should focus on first nav item
    
    // Navigate with arrow keys
    await page.keyboard.press('ArrowRight')
    await page.keyboard.press('Enter')
    
    // Should navigate to the focused section
    // Note: This test might need adjustment based on actual keyboard navigation implementation
    await page.waitForTimeout(500)
  })
})
