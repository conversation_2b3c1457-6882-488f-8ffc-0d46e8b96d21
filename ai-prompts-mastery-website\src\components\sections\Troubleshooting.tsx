import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import {
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  XCircle,
  HelpCircle,
  Lightbulb,
  Target,
  RefreshCw,
  Search,
  Settings
} from 'lucide-react'
import CodeBlock from '../ui/CodeBlock'

const Troubleshooting: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const issuesRef = useRef<HTMLDivElement>(null)
  const solutionsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Animate section entrance
    if (sectionRef.current) {
      gsap.fromTo(
        '.section-header',
        { y: 80, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: 'power3.out' }
      )
    }

    // Animate issue cards
    if (issuesRef.current) {
      gsap.fromTo(
        '.issue-card',
        { y: 60, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: issuesRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    // Animate solution cards
    if (solutionsRef.current) {
      gsap.fromTo(
        '.solution-card',
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: solutionsRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const commonIssues = [
    {
      title: 'AI Responses Are Too Generic or Vague',
      symptoms: [
        'Responses lack specificity',
        'Generic advice without context',
        'No concrete examples or actionable steps'
      ],
      icon: HelpCircle,
      color: 'from-red-500 to-pink-500',
      severity: 'High',
      solutions: [
        'Add specific context about your situation',
        'Request concrete examples explicitly',
        'Define success criteria clearly',
        'Use constraint prompting with specific requirements'
      ],
      example: {
        bad: `Help me write better`,
        good: `I'm writing a technical blog post about microservices for senior developers.
Help me improve this introduction paragraph [PASTE TEXT] by making it more
engaging while maintaining technical accuracy. Include specific examples
of microservices benefits.`
      }
    },
    {
      title: 'AI Ignores Important Instructions',
      symptoms: [
        'Key requirements are overlooked',
        'Instructions buried in long prompts are missed',
        'Inconsistent adherence to guidelines'
      ],
      icon: XCircle,
      color: 'from-orange-500 to-red-500',
      severity: 'High',
      solutions: [
        'Use structural formatting (headers, bullet points)',
        'Repeat critical instructions multiple times',
        'Use emphasis markers (**bold**, *italics*, CAPS)',
        'End with summary of key requirements'
      ],
      example: {
        bad: `Write a report about market trends and make sure to include data from 2024
and focus on the tech sector and keep it under 500 words`,
        good: `# Market Trends Report Request

**Primary Focus**: Technology sector
**Time Period**: 2024 data only
**Length Limit**: Maximum 500 words

Requirements:
1. Include specific 2024 data points
2. Focus exclusively on tech sector
3. Stay under 500 words

CRITICAL: Must include 2024 data and stay under 500 words.`
      }
    },
    {
      title: 'Inconsistent Response Quality',
      symptoms: [
        'Quality varies between similar requests',
        'Sometimes detailed, sometimes superficial',
        'Unpredictable depth of analysis'
      ],
      icon: RefreshCw,
      color: 'from-yellow-500 to-orange-500',
      severity: 'Medium',
      solutions: [
        'Standardize your prompts with templates',
        'Include quality criteria in prompts',
        'Use evaluation frameworks',
        'Provide examples of desired output quality'
      ],
      example: {
        bad: `Analyze this data`,
        good: `Analyze this data using the following framework:
1. Data Quality Assessment (completeness, accuracy, relevance)
2. Key Patterns and Trends (with statistical significance)
3. Business Implications (actionable insights)
4. Recommendations (specific, measurable actions)
5. Confidence Levels (high/medium/low for each finding)

Provide at least 3 specific insights per section.`
      }
    }
  ]

  const debuggingSteps = [
    {
      step: 1,
      title: 'Isolate Components',
      description: 'Test each part of your prompt separately',
      icon: Search,
      actions: [
        'Remove complex instructions one by one',
        'Test with minimal prompt first',
        'Add complexity gradually',
        'Identify which component causes issues'
      ]
    },
    {
      step: 2,
      title: 'Simplify Gradually',
      description: 'Remove complexity until you find the breaking point',
      icon: Target,
      actions: [
        'Start with basic version of your prompt',
        'Add one requirement at a time',
        'Test after each addition',
        'Note where quality degrades'
      ]
    },
    {
      step: 3,
      title: 'Add Explicit Instructions',
      description: 'Make implicit assumptions explicit',
      icon: Lightbulb,
      actions: [
        'State obvious requirements clearly',
        'Define technical terms',
        'Specify desired format',
        'Include examples of good output'
      ]
    },
    {
      step: 4,
      title: 'Test Edge Cases',
      description: 'Try variations to understand boundaries',
      icon: Settings,
      actions: [
        'Test with different input types',
        'Try extreme values or edge cases',
        'Vary the prompt structure',
        'Test with different AI models'
      ]
    }
  ]

  const quickFixes = [
    {
      problem: 'Too verbose',
      solution: 'Add length constraints',
      example: '"Summarize in exactly 3 bullet points"'
    },
    {
      problem: 'Too brief',
      solution: 'Request elaboration',
      example: '"Expand on each point with specific examples"'
    },
    {
      problem: 'Off-topic',
      solution: 'Clarify focus',
      example: '"Focus specifically on [EXACT REQUIREMENT]"'
    },
    {
      problem: 'Inconsistent format',
      solution: 'Specify structure',
      example: '"Use this exact format: 1. Problem 2. Solution 3. Example"'
    },
    {
      problem: 'Lacks examples',
      solution: 'Explicitly request them',
      example: '"Include at least 2 concrete examples for each point"'
    },
    {
      problem: 'Too technical',
      solution: 'Specify audience',
      example: '"Explain for non-technical business stakeholders"'
    }
  ]

  return (
    <section ref={sectionRef} id="troubleshooting" className="section">
      <div className="section-container">
        {/* Header */}
        <div className="section-header text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl mb-6">
            <AlertTriangle className="w-8 h-8 text-white" />
          </div>
          <h1 className="heading-1 mb-6 text-white">
            Troubleshooting Guide
          </h1>
          <p className="body-large max-w-3xl mx-auto mb-8">
            Common issues and solutions for prompt engineering. Debug and fix
            problems with your AI interactions using systematic approaches.
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-400">
            <CheckCircle className="w-4 h-4 text-red-500" />
            <span>Problem Solving</span>
            <span>•</span>
            <span>15 min read</span>
            <span>•</span>
            <span>Practical Solutions</span>
          </div>
        </div>

        {/* Common Issues */}
        <div ref={issuesRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Common Issues & Solutions
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Identify and fix the most frequent prompt engineering problems
            </p>
          </div>

          <div className="space-y-8">
            {commonIssues.map((issue, index) => {
              const Icon = issue.icon
              return (
                <div key={index} className="issue-card card p-8">
                  <div className="flex items-start space-x-6 mb-6">
                    <div className={`w-12 h-12 bg-gradient-to-br ${issue.color} rounded-xl flex items-center justify-center flex-shrink-0`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-xl font-semibold text-white">
                          {issue.title}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          issue.severity === 'High'
                            ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                            : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                        }`}>
                          {issue.severity} Priority
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Symptoms & Solutions */}
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                          <XCircle className="w-5 h-5 mr-2 text-red-400" />
                          Symptoms
                        </h4>
                        <div className="space-y-2">
                          {issue.symptoms.map((symptom, idx) => (
                            <div key={idx} className="flex items-start space-x-2">
                              <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-slate-300 text-sm">{symptom}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                          <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
                          Solutions
                        </h4>
                        <div className="space-y-2">
                          {issue.solutions.map((solution, idx) => (
                            <div key={idx} className="flex items-start space-x-2">
                              <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-slate-300 text-sm">{solution}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Before/After Examples */}
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-lg font-medium text-white mb-3">Before & After</h4>

                        <div className="space-y-4">
                          <div>
                            <p className="text-sm font-medium text-red-400 mb-2">❌ Problematic:</p>
                            <CodeBlock
                              code={issue.example.bad}
                              language="text"
                              showLineNumbers={false}
                              copyable={true}
                              className="text-xs"
                            />
                          </div>

                          <div>
                            <p className="text-sm font-medium text-green-400 mb-2">✅ Improved:</p>
                            <CodeBlock
                              code={issue.example.good}
                              language="text"
                              showLineNumbers={false}
                              copyable={true}
                              className="text-xs"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Systematic Debugging Process */}
        <div ref={solutionsRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Systematic Debugging Process
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Follow this step-by-step process to diagnose and fix any prompt engineering issue
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {debuggingSteps.map((step, index) => {
              const Icon = step.icon
              return (
                <div key={index} className="solution-card card card-hover p-6 text-center group">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-6 h-6 text-white" />
                  </div>

                  <div className="w-8 h-8 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-sm font-bold text-white">{step.step}</span>
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-3">
                    {step.title}
                  </h3>

                  <p className="text-slate-400 text-sm mb-4 leading-relaxed">
                    {step.description}
                  </p>

                  <div className="space-y-2">
                    {step.actions.map((action, idx) => (
                      <div key={idx} className="flex items-start space-x-2 text-left">
                        <div className="w-1 h-1 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-xs text-slate-400">{action}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Quick Fixes */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Quick Fixes Reference
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Common problems and their immediate solutions
            </p>
          </div>

          <div className="card overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-700/50">
                    <th className="text-left p-6 text-white font-semibold">Problem</th>
                    <th className="text-left p-6 text-white font-semibold">Quick Solution</th>
                    <th className="text-left p-6 text-white font-semibold">Example Fix</th>
                  </tr>
                </thead>
                <tbody>
                  {quickFixes.map((fix, index) => (
                    <tr key={index} className="border-b border-slate-700/30 hover:bg-slate-800/30 transition-colors">
                      <td className="p-6 font-medium text-slate-200">{fix.problem}</td>
                      <td className="p-6 text-slate-300">{fix.solution}</td>
                      <td className="p-6">
                        <code className="px-2 py-1 bg-slate-800 rounded text-sm text-primary-300">
                          {fix.example}
                        </code>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Prompt Validation Framework */}
        <div className="mb-20">
          <div className="card p-8 bg-gradient-to-br from-red-500/10 to-pink-500/10 border border-red-500/20">
            <h3 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <CheckCircle className="w-6 h-6 mr-3 text-red-400" />
              Prompt Validation Checklist
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-lg font-medium text-white mb-4">Before Finalizing:</h4>
                <div className="space-y-3">
                  {[
                    'Is the objective clear and specific?',
                    'Are the constraints well-defined?',
                    'Is the context sufficient?',
                    'Are the success criteria explicit?'
                  ].map((item, idx) => (
                    <label key={idx} className="flex items-center space-x-3 cursor-pointer">
                      <input type="checkbox" className="w-4 h-4 text-red-500 bg-slate-800 border-slate-600 rounded focus:ring-red-500" />
                      <span className="text-slate-300 text-sm">{item}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-lg font-medium text-white mb-4">Quality Checks:</h4>
                <div className="space-y-3">
                  {[
                    'Have I provided examples if needed?',
                    'Is the format/structure specified?',
                    'Are there any ambiguous terms?',
                    'Have I tested with variations?'
                  ].map((item, idx) => (
                    <label key={idx} className="flex items-center space-x-3 cursor-pointer">
                      <input type="checkbox" className="w-4 h-4 text-red-500 bg-slate-800 border-slate-600 rounded focus:ring-red-500" />
                      <span className="text-slate-300 text-sm">{item}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="text-center">
          <div className="card p-8 bg-gradient-to-br from-red-500/10 to-pink-500/10 border border-red-500/20">
            <h3 className="text-2xl font-semibold text-white mb-4">
              Keep Your Prompts Optimized
            </h3>
            <p className="body-normal mb-6 max-w-2xl mx-auto">
              Once you've fixed the issues, learn how to continuously improve and
              maintain your prompts for long-term success.
            </p>
            <a
              href="/section/maintenance"
              className="btn btn-primary px-8 py-3 text-lg font-semibold inline-flex items-center group"
            >
              <span>Maintenance & Optimization</span>
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Troubleshooting
