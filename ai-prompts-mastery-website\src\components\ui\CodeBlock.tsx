import React, { useState, useRef, useEffect } from 'react'
import { <PERSON>rism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { Copy, Check, Download, Play } from 'lucide-react'
import { gsap } from 'gsap'

interface CodeBlockProps {
  code: string
  language: string
  title?: string
  filename?: string
  showLineNumbers?: boolean
  highlightLines?: number[]
  copyable?: boolean
  downloadable?: boolean
  executable?: boolean
  onExecute?: () => void
  className?: string
}

const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language,
  title,
  filename,
  showLineNumbers = true,
  highlightLines = [],
  copyable = true,
  downloadable = false,
  executable = false,
  onExecute,
  className = ''
}) => {
  const [copied, setCopied] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const codeRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  useEffect(() => {
    if (copied && buttonRef.current) {
      gsap.fromTo(
        buttonRef.current,
        { scale: 1 },
        { scale: 1.1, duration: 0.1, yoyo: true, repeat: 1 }
      )
    }
  }, [copied])

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy code:', err)
    }
  }

  const handleDownload = () => {
    const blob = new Blob([code], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename || `code.${language}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
    
    if (codeRef.current) {
      gsap.to(codeRef.current, {
        height: isExpanded ? 'auto' : 'auto',
        duration: 0.3,
        ease: 'power2.out'
      })
    }
  }

  const customStyle = {
    ...oneDark,
    'pre[class*="language-"]': {
      ...oneDark['pre[class*="language-"]'],
      background: 'transparent',
      margin: 0,
      padding: '1rem',
      fontSize: '0.875rem',
      lineHeight: '1.5'
    },
    'code[class*="language-"]': {
      ...oneDark['code[class*="language-"]'],
      background: 'transparent',
      fontSize: '0.875rem'
    }
  }

  const shouldTruncate = code.split('\n').length > 20
  const displayCode = shouldTruncate && !isExpanded 
    ? code.split('\n').slice(0, 20).join('\n') + '\n...'
    : code

  return (
    <div className={`code-block group ${className}`}>
      {/* Header */}
      {(title || filename || copyable || downloadable || executable) && (
        <div className="flex items-center justify-between bg-slate-800/80 border-b border-slate-700/50 px-4 py-3">
          <div className="flex items-center space-x-3">
            {(title || filename) && (
              <div>
                {title && (
                  <h4 className="text-sm font-medium text-slate-200">{title}</h4>
                )}
                {filename && (
                  <p className="text-xs text-slate-400 font-mono">{filename}</p>
                )}
              </div>
            )}
            <span className="px-2 py-1 bg-slate-700/50 rounded text-xs text-slate-300 font-mono">
              {language}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {executable && onExecute && (
              <button
                onClick={onExecute}
                className="p-2 text-slate-400 hover:text-green-400 hover:bg-slate-700/50 rounded transition-colors"
                title="Execute code"
              >
                <Play className="w-4 h-4" />
              </button>
            )}
            
            {downloadable && (
              <button
                onClick={handleDownload}
                className="p-2 text-slate-400 hover:text-blue-400 hover:bg-slate-700/50 rounded transition-colors"
                title="Download code"
              >
                <Download className="w-4 h-4" />
              </button>
            )}
            
            {copyable && (
              <button
                ref={buttonRef}
                onClick={handleCopy}
                className="p-2 text-slate-400 hover:text-primary-400 hover:bg-slate-700/50 rounded transition-colors"
                title={copied ? 'Copied!' : 'Copy code'}
              >
                {copied ? (
                  <Check className="w-4 h-4 text-green-400" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
            )}
          </div>
        </div>
      )}

      {/* Code Content */}
      <div 
        ref={codeRef}
        className="relative overflow-hidden"
        style={{ maxHeight: shouldTruncate && !isExpanded ? '500px' : 'none' }}
      >
        <SyntaxHighlighter
          language={language}
          style={customStyle}
          showLineNumbers={showLineNumbers}
          wrapLines={true}
          lineProps={(lineNumber) => ({
            style: {
              backgroundColor: highlightLines.includes(lineNumber) 
                ? 'rgba(59, 130, 246, 0.1)' 
                : 'transparent',
              borderLeft: highlightLines.includes(lineNumber)
                ? '3px solid rgb(59, 130, 246)'
                : 'none',
              paddingLeft: highlightLines.includes(lineNumber) ? '0.5rem' : '0'
            }
          })}
          customStyle={{
            background: 'rgba(15, 23, 42, 0.8)',
            border: 'none',
            borderRadius: 0
          }}
        >
          {displayCode}
        </SyntaxHighlighter>
        
        {/* Fade overlay for truncated content */}
        {shouldTruncate && !isExpanded && (
          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-slate-900/90 to-transparent pointer-events-none" />
        )}
      </div>

      {/* Expand/Collapse Button */}
      {shouldTruncate && (
        <div className="border-t border-slate-700/50 bg-slate-800/50 px-4 py-2">
          <button
            onClick={toggleExpanded}
            className="text-sm text-primary-400 hover:text-primary-300 transition-colors"
          >
            {isExpanded ? 'Show Less' : `Show More (${code.split('\n').length - 20} more lines)`}
          </button>
        </div>
      )}
    </div>
  )
}

export default CodeBlock
