import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import {
  Wrench,
  CheckCircle,
  ArrowRight,
  TrendingUp,
  BarChart3,
  RefreshCw,
  Target,
  Zap,
  FileText
} from 'lucide-react'
import CodeBlock from '../ui/CodeBlock'

const Maintenance: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const processRef = useRef<HTMLDivElement>(null)
  const strategiesRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Animate section entrance
    if (sectionRef.current) {
      gsap.fromTo(
        '.section-header',
        { y: 80, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: 'power3.out' }
      )
    }

    // Animate process cards
    if (processRef.current) {
      gsap.fromTo(
        '.process-card',
        { y: 60, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: processRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    // Animate strategy cards
    if (strategiesRef.current) {
      gsap.fromTo(
        '.strategy-card',
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: strategiesRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const improvementProcess = [
    {
      phase: 'Week 1',
      title: 'Deploy Initial Prompt',
      description: 'Launch your prompt and start collecting data',
      icon: Target,
      color: 'from-blue-500 to-cyan-500',
      activities: [
        'Deploy prompt in production environment',
        'Set up monitoring and logging',
        'Establish baseline metrics',
        'Document initial performance'
      ]
    },
    {
      phase: 'Week 2',
      title: 'Collect Performance Data',
      description: 'Gather user feedback and performance metrics',
      icon: BarChart3,
      color: 'from-green-500 to-emerald-500',
      activities: [
        'Monitor response quality scores',
        'Collect user satisfaction feedback',
        'Track completion rates',
        'Identify common failure patterns'
      ]
    },
    {
      phase: 'Week 3',
      title: 'Analyze & Identify Improvements',
      description: 'Review data and find optimization opportunities',
      icon: TrendingUp,
      color: 'from-purple-500 to-pink-500',
      activities: [
        'Analyze performance patterns',
        'Identify bottlenecks and issues',
        'Prioritize improvement opportunities',
        'Design optimization experiments'
      ]
    },
    {
      phase: 'Week 4',
      title: 'Implement Refined Version',
      description: 'Deploy improvements and measure impact',
      icon: RefreshCw,
      color: 'from-orange-500 to-red-500',
      activities: [
        'Implement prompt improvements',
        'A/B test new vs old versions',
        'Monitor impact on key metrics',
        'Document lessons learned'
      ]
    }
  ]

  const optimizationStrategies = [
    {
      title: 'Prompt Compression',
      description: 'Remove unnecessary words while maintaining effectiveness',
      icon: Zap,
      example: {
        before: `I would like you to please help me by providing a comprehensive analysis
of the current market situation in the technology sector, and I would
appreciate it if you could include relevant data and insights`,
        after: `Analyze the current technology market. Include relevant data and insights.`
      },
      benefits: [
        'Faster processing',
        'Lower token costs',
        'Clearer instructions',
        'Better focus'
      ]
    },
    {
      title: 'Context Optimization',
      description: 'Provide just enough context—not too little, not too much',
      icon: Target,
      formula: `Optimal Context = Essential Background + Specific Requirements + Success Criteria + Format Preferences`,
      tips: [
        'Include only relevant background information',
        'Be specific about what you want',
        'Define how to measure success',
        'Specify desired output format'
      ]
    },
    {
      title: 'Template Development',
      description: 'Create reusable templates for common tasks',
      icon: FileText,
      template: `# Analysis Template
**Objective**: [WHAT TO ANALYZE]
**Context**: [RELEVANT BACKGROUND]
**Framework**: [ANALYTICAL APPROACH]
**Output**: [DESIRED FORMAT]
**Constraints**: [LIMITATIONS OR REQUIREMENTS]`,
      benefits: [
        'Consistent quality',
        'Faster prompt creation',
        'Easier maintenance',
        'Team standardization'
      ]
    }
  ]

  const performanceMetrics = [
    {
      metric: 'Response Relevance',
      description: 'How well responses match your intent',
      measurement: '1-10 scale rating',
      target: '> 8.0 average'
    },
    {
      metric: 'Consistency',
      description: 'Variation in quality across similar requests',
      measurement: 'Standard deviation of quality scores',
      target: '< 1.5 std dev'
    },
    {
      metric: 'Efficiency',
      description: 'Time and tokens required for desired outcomes',
      measurement: 'Tokens per successful response',
      target: '< 500 tokens avg'
    },
    {
      metric: 'User Satisfaction',
      description: 'Feedback from end users or stakeholders',
      measurement: 'User rating surveys',
      target: '> 4.0/5.0 rating'
    }
  ]

  const abTestingExample = `# A/B Testing Framework

## Version A: Original Prompt
You are a data analyst. Analyze this sales data and provide insights.

## Version B: Enhanced Prompt
You are a senior data analyst with expertise in retail sales.

Analyze this sales data using the following framework:
1. Revenue trends and patterns
2. Customer segment performance
3. Product category insights
4. Actionable recommendations

Provide specific metrics and confidence levels for each insight.

## Test Criteria:
- Response quality (1-10 scale)
- Task completion rate
- User preference
- Time to satisfactory result

## Results:
Version B showed 40% improvement in response quality
and 60% higher user satisfaction scores.`

  return (
    <section ref={sectionRef} id="maintenance" className="section">
      <div className="section-container">
        {/* Header */}
        <div className="section-header text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl mb-6">
            <Wrench className="w-8 h-8 text-white" />
          </div>
          <h1 className="heading-1 mb-6 text-white">
            Maintenance & Optimization
          </h1>
          <p className="body-large max-w-3xl mx-auto mb-8">
            How to refine and improve your prompts over time. Continuous improvement
            strategies for better AI interactions and long-term success.
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-400">
            <CheckCircle className="w-4 h-4 text-indigo-500" />
            <span>Optimization Guide</span>
            <span>•</span>
            <span>20 min read</span>
            <span>•</span>
            <span>Continuous Improvement</span>
          </div>
        </div>

        {/* Continuous Improvement Process */}
        <div ref={processRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Continuous Improvement Process
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Follow this systematic 4-week cycle to continuously optimize your prompts
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {improvementProcess.map((phase, index) => {
              const Icon = phase.icon
              return (
                <div key={index} className="process-card card card-hover p-6 text-center group">
                  <div className={`w-12 h-12 bg-gradient-to-br ${phase.color} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>

                  <div className="px-3 py-1 bg-slate-700 rounded-full text-xs font-medium text-slate-300 mb-3 inline-block">
                    {phase.phase}
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-3">
                    {phase.title}
                  </h3>

                  <p className="text-slate-400 text-sm mb-4 leading-relaxed">
                    {phase.description}
                  </p>

                  <div className="space-y-2">
                    {phase.activities.map((activity, idx) => (
                      <div key={idx} className="flex items-start space-x-2 text-left">
                        <div className="w-1 h-1 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-xs text-slate-400">{activity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Performance Monitoring */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Performance Monitoring
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Track these key metrics to measure and improve your prompt effectiveness
            </p>
          </div>

          <div className="card overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-700/50">
                    <th className="text-left p-6 text-white font-semibold">Metric</th>
                    <th className="text-left p-6 text-white font-semibold">Description</th>
                    <th className="text-left p-6 text-white font-semibold">How to Measure</th>
                    <th className="text-left p-6 text-white font-semibold">Target</th>
                  </tr>
                </thead>
                <tbody>
                  {performanceMetrics.map((metric, index) => (
                    <tr key={index} className="border-b border-slate-700/30 hover:bg-slate-800/30 transition-colors">
                      <td className="p-6 font-medium text-slate-200">{metric.metric}</td>
                      <td className="p-6 text-slate-300">{metric.description}</td>
                      <td className="p-6 text-slate-300">{metric.measurement}</td>
                      <td className="p-6">
                        <span className="px-2 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded text-sm">
                          {metric.target}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Optimization Strategies */}
        <div ref={strategiesRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Optimization Strategies
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Proven techniques to make your prompts more effective and efficient
            </p>
          </div>

          <div className="space-y-8">
            {optimizationStrategies.map((strategy, index) => {
              const Icon = strategy.icon
              return (
                <div key={index} className="strategy-card card p-8">
                  <div className="flex items-start space-x-6 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {strategy.title}
                      </h3>
                      <p className="text-slate-400 leading-relaxed">
                        {strategy.description}
                      </p>
                    </div>
                  </div>

                  {strategy.example && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className="text-lg font-medium text-white mb-3">Before:</h4>
                        <CodeBlock
                          code={strategy.example.before}
                          language="text"
                          showLineNumbers={false}
                          copyable={true}
                          className="text-sm"
                        />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-white mb-3">After:</h4>
                        <CodeBlock
                          code={strategy.example.after}
                          language="text"
                          showLineNumbers={false}
                          copyable={true}
                          className="text-sm"
                        />
                      </div>
                    </div>
                  )}

                  {strategy.formula && (
                    <div className="mb-6">
                      <h4 className="text-lg font-medium text-white mb-3">Formula:</h4>
                      <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4">
                        <code className="text-primary-300 font-mono text-sm">{strategy.formula}</code>
                      </div>
                    </div>
                  )}

                  {strategy.template && (
                    <div className="mb-6">
                      <h4 className="text-lg font-medium text-white mb-3">Template:</h4>
                      <CodeBlock
                        code={strategy.template}
                        language="text"
                        showLineNumbers={false}
                        copyable={true}
                        className="text-sm"
                      />
                    </div>
                  )}

                  {(strategy.benefits || strategy.tips) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {strategy.benefits && (
                        <div>
                          <h4 className="text-lg font-medium text-white mb-3">Benefits:</h4>
                          <div className="space-y-2">
                            {strategy.benefits.map((benefit, idx) => (
                              <div key={idx} className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                                <span className="text-slate-300 text-sm">{benefit}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {strategy.tips && (
                        <div>
                          <h4 className="text-lg font-medium text-white mb-3">Tips:</h4>
                          <div className="space-y-2">
                            {strategy.tips.map((tip, idx) => (
                              <div key={idx} className="flex items-start space-x-2">
                                <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                                <span className="text-slate-300 text-sm">{tip}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* A/B Testing Example */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              A/B Testing for Prompts
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Real example of how A/B testing improved prompt performance by 40%
            </p>
          </div>

          <CodeBlock
            code={abTestingExample}
            language="text"
            title="A/B Testing Case Study"
            showLineNumbers={false}
            copyable={true}
          />
        </div>

        {/* Next Steps */}
        <div className="text-center">
          <div className="card p-8 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border border-indigo-500/20">
            <h3 className="text-2xl font-semibold text-white mb-4">
              Ensure Safe & Compliant Operations
            </h3>
            <p className="body-normal mb-6 max-w-2xl mx-auto">
              As you optimize your prompts, make sure you're following best practices
              for safety, ethics, and compliance in AI systems.
            </p>
            <a
              href="/section/safety"
              className="btn btn-primary px-8 py-3 text-lg font-semibold inline-flex items-center group"
            >
              <span>Safety & Compliance</span>
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Maintenance
