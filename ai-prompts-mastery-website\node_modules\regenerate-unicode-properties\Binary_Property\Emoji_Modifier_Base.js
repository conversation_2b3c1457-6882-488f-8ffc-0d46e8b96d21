const set = require('regenerate')(0x261D, 0x26F9, 0x1F385, 0x1F3C7, 0x1F47C, 0x1F48F, 0x1F491, 0x1F4AA, 0x1F57A, 0x1F590, 0x1F6A3, 0x1F6C0, 0x1F6CC, 0x1F90C, 0x1F90F, 0x1F926, 0x1F977, 0x1F9BB);
set.addRange(0x270A, 0x270D).addRange(0x1F3C2, 0x1F3C4).addRange(0x1F3CA, 0x1F3CC).addRange(0x1F442, 0x1F443).addRange(0x1F446, 0x1F450).addRange(0x1F466, 0x1F478).addRange(0x1F481, 0x1F483).addRange(0x1F485, 0x1F487).addRange(0x1F574, 0x1F575).addRange(0x1F595, 0x1F596).addRange(0x1F645, 0x1F647).addRange(0x1F64B, 0x1F64F).addRange(0x1F6B4, 0x1F6B6).addRange(0x1F918, 0x1F91F).addRange(0x1F930, 0x1F939).addRange(0x1F93C, 0x1F93E).addRange(0x1F9B5, 0x1F9B6).addRange(0x1F9B8, 0x1F9B9).addRange(0x1F9CD, 0x1F9CF).addRange(0x1F9D1, 0x1F9DD).addRange(0x1FAC3, 0x1FAC5).addRange(0x1FAF0, 0x1FAF8);
exports.characters = set;
