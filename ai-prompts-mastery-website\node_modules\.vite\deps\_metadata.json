{"hash": "9722ddc5", "configHash": "f8614c0b", "lockfileHash": "03f1d36e", "browserHash": "0004a316", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c85a069b", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "3e8599b7", "needsInterop": true}, "gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "bd0ed52d", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "c218e70d", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3dbec0e4", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "0db4f7ba", "needsInterop": true}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "8e15180a", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "cec369d6", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "54655755", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "cc782539", "needsInterop": false}}, "chunks": {"chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-PJEEZAML": {"file": "chunk-PJEEZAML.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}