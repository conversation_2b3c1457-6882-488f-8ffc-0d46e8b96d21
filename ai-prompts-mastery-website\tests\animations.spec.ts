import { test, expect } from '@playwright/test'

test.describe('Animations', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should have smooth page entrance animations', async ({ page }) => {
    // Check that hero elements have animation classes
    await expect(page.locator('.hero-title')).toBeVisible()
    await expect(page.locator('.hero-subtitle')).toBeVisible()
    await expect(page.locator('.hero-description')).toBeVisible()
    await expect(page.locator('.hero-cta')).toBeVisible()
    
    // Wait for animations to complete
    await page.waitForTimeout(2000)
    
    // Check elements are in their final positions (opacity should be 1)
    const heroTitle = page.locator('.hero-title')
    const opacity = await heroTitle.evaluate(el => window.getComputedStyle(el).opacity)
    expect(parseFloat(opacity)).toBeGreaterThan(0.9)
  })

  test('should have scroll-triggered animations', async ({ page }) => {
    // Scroll to features section
    await page.locator('text=Why This Guide is Different').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // Check feature cards are visible and animated
    const featureCards = page.locator('.feature-card')
    const count = await featureCards.count()
    expect(count).toBeGreaterThan(0)
    
    // Check that cards have proper opacity after animation
    for (let i = 0; i < count; i++) {
      const card = featureCards.nth(i)
      await expect(card).toBeVisible()
      const opacity = await card.evaluate(el => window.getComputedStyle(el).opacity)
      expect(parseFloat(opacity)).toBeGreaterThan(0.9)
    }
  })

  test('should have working hover animations', async ({ page }) => {
    // Set desktop viewport for hover effects
    await page.setViewportSize({ width: 1200, height: 800 })
    
    // Find a hoverable element (feature card)
    const featureCard = page.locator('.feature-card').first()
    await expect(featureCard).toBeVisible()
    
    // Get initial transform
    const initialTransform = await featureCard.evaluate(el => window.getComputedStyle(el).transform)
    
    // Hover over the card
    await featureCard.hover()
    await page.waitForTimeout(500)
    
    // Check that transform has changed (indicating hover animation)
    const hoverTransform = await featureCard.evaluate(el => window.getComputedStyle(el).transform)
    
    // Note: This test might need adjustment based on actual hover implementation
    // The transform should be different when hovering
  })

  test('should respect reduced motion preferences', async ({ page }) => {
    // Set reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' })
    
    // Reload page with reduced motion
    await page.reload()
    
    // Check that animations are disabled or significantly reduced
    // This would require checking animation durations or transform values
    const heroTitle = page.locator('.hero-title')
    await expect(heroTitle).toBeVisible()
    
    // In reduced motion mode, animations should be very fast or disabled
    // This test would need to be implemented based on the actual reduced motion handling
  })

  test('should have smooth navigation transitions', async ({ page }) => {
    // Navigate to a section
    await page.click('text=Owner\'s Manual')
    await page.waitForLoadState('networkidle')
    
    // Check that page content is visible
    await expect(page.locator('h1')).toContainText('Owner\'s Manual')
    
    // Check that content has proper opacity (indicating animation completed)
    const content = page.locator('.section-header')
    await expect(content).toBeVisible()
    const opacity = await content.evaluate(el => window.getComputedStyle(el).opacity)
    expect(parseFloat(opacity)).toBeGreaterThan(0.9)
  })

  test('should have working scroll progress animation', async ({ page }) => {
    // Check initial scroll progress
    const progressBar = page.locator('.h-full.bg-gradient-to-r')
    await expect(progressBar).toBeVisible()
    
    // Get initial width
    const initialWidth = await progressBar.evaluate(el => el.style.width || '0%')
    
    // Scroll down
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight / 2))
    await page.waitForTimeout(500)
    
    // Check that progress has increased
    const newWidth = await progressBar.evaluate(el => el.style.width || '0%')
    
    // Parse percentages and compare
    const initialPercent = parseFloat(initialWidth.replace('%', ''))
    const newPercent = parseFloat(newWidth.replace('%', ''))
    expect(newPercent).toBeGreaterThan(initialPercent)
  })

  test('should have staggered animations for lists', async ({ page }) => {
    // Navigate to a section with staggered animations
    await page.click('text=Technical Specs')
    await page.waitForLoadState('networkidle')
    
    // Scroll to architecture cards
    await page.locator('text=AI System Architectures').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1500) // Wait for stagger animation
    
    // Check that architecture cards are visible
    const architectureCards = page.locator('.architecture-card')
    const count = await architectureCards.count()
    expect(count).toBeGreaterThan(0)
    
    // All cards should be visible after stagger animation
    for (let i = 0; i < count; i++) {
      const card = architectureCards.nth(i)
      await expect(card).toBeVisible()
    }
  })

  test('should have smooth mobile animations', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check that animations work on mobile
    await expect(page.locator('.hero-title')).toBeVisible()
    
    // Open mobile menu
    const menuButton = page.locator('button[aria-label="Toggle navigation menu"]')
    await menuButton.click()
    
    // Check menu animation
    await page.waitForTimeout(500)
    await expect(page.locator('text=Owner\'s Manual')).toBeVisible()
    
    // Close menu
    await menuButton.click()
    await page.waitForTimeout(500)
  })

  test('should maintain 60fps during animations', async ({ page }) => {
    // This test would require performance monitoring
    // Start performance monitoring
    await page.evaluate(() => {
      (window as any).performanceData = {
        frames: 0,
        startTime: performance.now()
      }
      
      function countFrames() {
        (window as any).performanceData.frames++
        requestAnimationFrame(countFrames)
      }
      requestAnimationFrame(countFrames)
    })
    
    // Trigger animations by scrolling
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight / 2)
    })
    
    // Wait for animations
    await page.waitForTimeout(2000)
    
    // Check frame rate
    const performanceData = await page.evaluate(() => (window as any).performanceData)
    const duration = performanceData.endTime || (performance.now() - performanceData.startTime)
    const fps = (performanceData.frames / duration) * 1000
    
    // Should maintain close to 60fps (allow some tolerance)
    expect(fps).toBeGreaterThan(45)
  })

  test('should have working GSAP animations', async ({ page }) => {
    // Check that GSAP is loaded
    const gsapLoaded = await page.evaluate(() => typeof (window as any).gsap !== 'undefined')
    expect(gsapLoaded).toBe(true)
    
    // Check ScrollTrigger is loaded
    const scrollTriggerLoaded = await page.evaluate(() => {
      return typeof (window as any).gsap?.ScrollTrigger !== 'undefined'
    })
    expect(scrollTriggerLoaded).toBe(true)
    
    // Navigate to a section and check animations
    await page.click('text=Advanced Techniques')
    await page.waitForLoadState('networkidle')
    
    // Scroll to trigger animations
    await page.locator('text=Cutting-Edge Prompt Engineering').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // Check that technique cards are animated
    const techniqueCards = page.locator('.technique-card')
    const count = await techniqueCards.count()
    expect(count).toBeGreaterThan(0)
    
    for (let i = 0; i < count; i++) {
      const card = techniqueCards.nth(i)
      await expect(card).toBeVisible()
    }
  })
})
