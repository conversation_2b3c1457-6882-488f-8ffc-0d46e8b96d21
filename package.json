{"name": "augment-code-testing", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^24.0.3", "typescript": "^5.8.3"}, "dependencies": {"@executeautomation/playwright-mcp-server": "^1.0.6", "@upstash/context7-mcp": "^1.0.14", "playwright": "^1.53.1"}}