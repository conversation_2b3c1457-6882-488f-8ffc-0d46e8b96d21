import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import {
  Shield,
  CheckCircle,
  ArrowRight,
  AlertTriangle,
  Users,
  Lock,
  Eye,
  Scale,
  Heart,
  FileText,
  UserCheck
} from 'lucide-react'
import CodeBlock from '../ui/CodeBlock'

const Safety: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const principlesRef = useRef<HTMLDivElement>(null)
  const guidelinesRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Animate section entrance
    if (sectionRef.current) {
      gsap.fromTo(
        '.section-header',
        { y: 80, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: 'power3.out' }
      )
    }

    // Animate principle cards
    if (principlesRef.current) {
      gsap.fromTo(
        '.principle-card',
        { y: 60, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: principlesRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    // Animate guideline cards
    if (guidelinesRef.current) {
      gsap.fromTo(
        '.guideline-card',
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: guidelinesRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const ethicalPrinciples = [
    {
      title: 'Transparency and Honesty',
      description: 'Clearly communicate AI limitations and be transparent about AI involvement',
      icon: Eye,
      color: 'from-blue-500 to-cyan-500',
      guidelines: [
        'Clearly communicate AI limitations',
        'Avoid prompts that encourage deception',
        'Be transparent about AI involvement in outputs',
        'Acknowledge uncertainty and confidence levels'
      ],
      example: `Include in prompts:
"Please indicate your confidence level for each claim and acknowledge any limitations in your knowledge or reasoning."`
    },
    {
      title: 'Bias Prevention and Mitigation',
      description: 'Ensure fair and balanced AI responses across all demographics',
      icon: Users,
      color: 'from-green-500 to-emerald-500',
      guidelines: [
        'Consider multiple perspectives and avoid bias',
        'Ensure balanced representation',
        'Acknowledge cultural sensitivities',
        'Test prompts across diverse scenarios'
      ],
      example: `"Consider multiple perspectives and avoid cultural, gender, or demographic bias.
If discussing people or groups, ensure balanced and respectful representation.
Acknowledge when topics may have cultural sensitivities."`
    },
    {
      title: 'Privacy and Data Protection',
      description: 'Protect user privacy and handle data responsibly',
      icon: Lock,
      color: 'from-purple-500 to-pink-500',
      guidelines: [
        'Never include PII in examples',
        'Avoid prompts that could lead to privacy violations',
        'Include data handling instructions',
        'Respect confidentiality requirements'
      ],
      example: `Privacy Guidelines:
- Use placeholder data like [USER_NAME] instead of real names
- Include: "Do not request or process personal identifying information"
- Specify data retention and handling requirements`
    },
    {
      title: 'Harm Prevention',
      description: 'Ensure AI outputs do not cause harm to individuals or society',
      icon: Heart,
      color: 'from-red-500 to-pink-500',
      guidelines: [
        'Prevent harmful content generation',
        'Respect human dignity and rights',
        'Consider potential misuse scenarios',
        'Include appropriate disclaimers'
      ],
      example: `Safety Checklist:
□ Could this prompt lead to harmful content?
□ Does it respect human dignity and rights?
□ Are there potential misuse scenarios?
□ Are safety guardrails in place?`
    }
  ]

  const complianceFrameworks = [
    {
      title: 'Content Policy Compliance',
      description: 'Understanding and adhering to AI safety mechanisms',
      icon: FileText,
      systems: [
        {
          name: 'Anthropic Claude',
          features: ['Constitutional AI framework', 'Harmlessness training', 'Face blindness for privacy', 'Content filtering layers']
        },
        {
          name: 'OpenAI GPT',
          features: ['Usage policy enforcement', 'Content filtering systems', 'Safety classifiers', 'Moderation API integration']
        },
        {
          name: 'Google Gemini',
          features: ['Responsible AI principles', 'Safety filters and warnings', 'Cultural sensitivity measures', 'Fact-checking integration']
        }
      ]
    },
    {
      title: 'Legal and Regulatory Considerations',
      description: 'Compliance with laws and regulations',
      icon: Scale,
      areas: [
        {
          category: 'Intellectual Property',
          guidelines: ['Avoid requesting copyrighted content reproduction', 'Include attribution requirements', 'Respect fair use principles', 'Consider derivative work implications']
        },
        {
          category: 'Professional Standards',
          guidelines: ['Include appropriate disclaimers', 'Emphasize need for professional consultation', 'Avoid specific advice requiring licensing', 'Maintain clear boundaries of AI capabilities']
        },
        {
          category: 'Data Governance',
          guidelines: ['Minimize data collection in prompts', 'Ensure data relevance and necessity', 'Respect data retention policies', 'Follow jurisdiction-specific regulations (GDPR, CCPA, etc.)']
        }
      ]
    }
  ]

  const safePromptPatterns = {
    analysis: `# Safe Analysis Pattern
"Analyze [TOPIC] objectively, considering:
1. Multiple legitimate perspectives
2. Potential benefits and risks
3. Ethical implications
4. Factual accuracy and sources
5. Appropriate disclaimers for sensitive topics"`,
    creative: `# Safe Creative Pattern
"Create [CONTENT] that is:
- Respectful of all individuals and groups
- Factually grounded where applicable
- Appropriate for general audiences
- Free from harmful stereotypes
- Inclusive and accessible"`
  }

  const riskAssessment = [
    {
      risk: 'Bias and Discrimination',
      level: 'High',
      mitigation: 'Test prompts across diverse scenarios, include bias prevention instructions',
      monitoring: 'Regular bias audits, diverse user feedback'
    },
    {
      risk: 'Privacy Violations',
      level: 'High',
      mitigation: 'Avoid PII in prompts, include privacy protection instructions',
      monitoring: 'Data handling audits, privacy compliance checks'
    },
    {
      risk: 'Misinformation',
      level: 'Medium',
      mitigation: 'Include fact-checking requirements, confidence indicators',
      monitoring: 'Accuracy verification, source validation'
    },
    {
      risk: 'Harmful Content',
      level: 'Medium',
      mitigation: 'Content filtering, safety guidelines in prompts',
      monitoring: 'Content review, user reporting systems'
    }
  ]

  return (
    <section ref={sectionRef} id="safety" className="section">
      <div className="section-container">
        {/* Header */}
        <div className="section-header text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-xl mb-6">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h1 className="heading-1 mb-6 text-white">
            Safety & Compliance
          </h1>
          <p className="body-large max-w-3xl mx-auto mb-8">
            Best practices and ethical considerations for AI prompts. Ensure safe,
            responsible, and compliant AI interactions in all your applications.
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-400">
            <CheckCircle className="w-4 h-4 text-teal-500" />
            <span>Critical Knowledge</span>
            <span>•</span>
            <span>25 min read</span>
            <span>•</span>
            <span>Safety & Ethics</span>
          </div>
        </div>

        {/* Ethical Principles */}
        <div ref={principlesRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Ethical Prompt Engineering Principles
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Core principles that should guide all AI prompt engineering activities
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {ethicalPrinciples.map((principle, index) => {
              const Icon = principle.icon
              return (
                <div key={index} className="principle-card card card-hover p-6 group">
                  <div className="flex items-start space-x-4 mb-6">
                    <div className={`w-12 h-12 bg-gradient-to-br ${principle.color} rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {principle.title}
                      </h3>
                      <p className="text-slate-400 leading-relaxed">
                        {principle.description}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="text-lg font-medium text-white mb-3">Guidelines:</h4>
                      <div className="space-y-2">
                        {principle.guidelines.map((guideline, idx) => (
                          <div key={idx} className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                            <span className="text-slate-300 text-sm">{guideline}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-lg font-medium text-white mb-3">Example Implementation:</h4>
                      <CodeBlock
                        code={principle.example}
                        language="text"
                        showLineNumbers={false}
                        copyable={true}
                        className="text-xs"
                      />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Safe Prompt Patterns */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Safe Prompt Patterns
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Proven templates that incorporate safety and ethical considerations
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="card p-6">
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <UserCheck className="w-5 h-5 mr-2 text-teal-400" />
                Safe Analysis Pattern
              </h3>
              <CodeBlock
                code={safePromptPatterns.analysis}
                language="text"
                showLineNumbers={false}
                copyable={true}
              />
            </div>

            <div className="card p-6">
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <Heart className="w-5 h-5 mr-2 text-teal-400" />
                Safe Creative Pattern
              </h3>
              <CodeBlock
                code={safePromptPatterns.creative}
                language="text"
                showLineNumbers={false}
                copyable={true}
              />
            </div>
          </div>
        </div>

        {/* Compliance Frameworks */}
        <div ref={guidelinesRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Compliance Frameworks
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Understanding legal and regulatory requirements for AI systems
            </p>
          </div>

          <div className="space-y-8">
            {complianceFrameworks.map((framework, index) => {
              const Icon = framework.icon
              return (
                <div key={index} className="guideline-card card p-8">
                  <div className="flex items-start space-x-6 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {framework.title}
                      </h3>
                      <p className="text-slate-400 leading-relaxed">
                        {framework.description}
                      </p>
                    </div>
                  </div>

                  {framework.systems && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {framework.systems.map((system, idx) => (
                        <div key={idx} className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4">
                          <h4 className="font-semibold text-white mb-3">{system.name}</h4>
                          <div className="space-y-2">
                            {system.features.map((feature, featureIdx) => (
                              <div key={featureIdx} className="flex items-start space-x-2">
                                <div className="w-1.5 h-1.5 bg-teal-400 rounded-full mt-2 flex-shrink-0"></div>
                                <span className="text-slate-300 text-sm">{feature}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {framework.areas && (
                    <div className="space-y-6">
                      {framework.areas.map((area, idx) => (
                        <div key={idx} className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-6">
                          <h4 className="font-semibold text-white mb-3">{area.category}</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {area.guidelines.map((guideline, guidelineIdx) => (
                              <div key={guidelineIdx} className="flex items-start space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                                <span className="text-slate-300 text-sm">{guideline}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Risk Assessment */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Risk Assessment Framework
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Identify, assess, and mitigate risks in AI prompt engineering
            </p>
          </div>

          <div className="card overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-700/50">
                    <th className="text-left p-6 text-white font-semibold">Risk Type</th>
                    <th className="text-left p-6 text-white font-semibold">Level</th>
                    <th className="text-left p-6 text-white font-semibold">Mitigation Strategy</th>
                    <th className="text-left p-6 text-white font-semibold">Monitoring</th>
                  </tr>
                </thead>
                <tbody>
                  {riskAssessment.map((risk, index) => (
                    <tr key={index} className="border-b border-slate-700/30 hover:bg-slate-800/30 transition-colors">
                      <td className="p-6 font-medium text-slate-200">{risk.risk}</td>
                      <td className="p-6">
                        <span className={`px-2 py-1 rounded text-sm font-medium ${
                          risk.level === 'High'
                            ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                            : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                        }`}>
                          {risk.level}
                        </span>
                      </td>
                      <td className="p-6 text-slate-300">{risk.mitigation}</td>
                      <td className="p-6 text-slate-300">{risk.monitoring}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Emergency Procedures */}
        <div className="mb-20">
          <div className="card p-8 bg-gradient-to-br from-red-500/10 to-pink-500/10 border border-red-500/20">
            <h3 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <AlertTriangle className="w-6 h-6 mr-3 text-red-400" />
              Incident Response Plan
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  step: '1. Immediate Response',
                  actions: ['Stop problematic prompt usage', 'Document the incident', 'Assess potential impact']
                },
                {
                  step: '2. Investigation',
                  actions: ['Analyze root cause', 'Review similar prompts', 'Consult with experts if needed']
                },
                {
                  step: '3. Remediation',
                  actions: ['Fix the prompt or process', 'Implement additional safeguards', 'Communicate with affected users']
                },
                {
                  step: '4. Prevention',
                  actions: ['Update safety procedures', 'Enhance testing protocols', 'Train team on lessons learned']
                }
              ].map((phase, idx) => (
                <div key={idx} className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4">
                  <h4 className="font-semibold text-white mb-3">{phase.step}</h4>
                  <div className="space-y-2">
                    {phase.actions.map((action, actionIdx) => (
                      <div key={actionIdx} className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-slate-300 text-sm">{action}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Conclusion */}
        <div className="text-center">
          <div className="card p-8 bg-gradient-to-br from-teal-500/10 to-cyan-500/10 border border-teal-500/20">
            <h3 className="text-2xl font-semibold text-white mb-4">
              Congratulations! You've Mastered AI Prompt Engineering
            </h3>
            <p className="body-normal mb-6 max-w-2xl mx-auto">
              You now have the knowledge and tools to create safe, effective, and ethical
              AI prompts. Continue practicing and stay updated with the latest developments
              in AI safety and prompt engineering.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <a
                href="/"
                className="btn btn-primary px-8 py-3 text-lg font-semibold inline-flex items-center group"
              >
                <span>Back to Home</span>
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
              </a>
              <a
                href="/section/owners-manual"
                className="btn btn-secondary px-8 py-3 text-lg font-semibold"
              >
                Review the Guide
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Safety
