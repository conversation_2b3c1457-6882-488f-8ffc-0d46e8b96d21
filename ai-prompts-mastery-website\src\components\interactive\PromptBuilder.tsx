import React, { useState, useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import {
  Settings,
  User,
  Target,
  FileText,
  Zap,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  Lightbulb
} from 'lucide-react'
import CodeBlock from '../ui/CodeBlock'

interface PromptSection {
  id: string
  title: string
  description: string
  placeholder: string
  value: string
  required: boolean
  icon: React.ComponentType<any>
}

const PromptBuilder: React.FC = () => {
  const [sections, setSections] = useState<PromptSection[]>([
    {
      id: 'role',
      title: 'Role Definition',
      description: 'Define who the AI should be (expert, assistant, etc.)',
      placeholder: 'You are a senior software architect with 15 years of experience...',
      value: '',
      required: true,
      icon: User
    },
    {
      id: 'context',
      title: 'Context & Background',
      description: 'Provide relevant background information',
      placeholder: 'The project involves building a scalable microservices architecture...',
      value: '',
      required: false,
      icon: FileText
    },
    {
      id: 'task',
      title: 'Task Definition',
      description: 'Clearly state what you want the AI to do',
      placeholder: 'Design a system architecture that can handle 1M+ users...',
      value: '',
      required: true,
      icon: Target
    },
    {
      id: 'constraints',
      title: 'Constraints & Requirements',
      description: 'Specify limitations, requirements, or guidelines',
      placeholder: 'Must use cloud-native technologies, budget under $10k/month...',
      value: '',
      required: false,
      icon: Settings
    },
    {
      id: 'output',
      title: 'Output Format',
      description: 'Specify how you want the response formatted',
      placeholder: 'Provide a detailed technical specification with diagrams...',
      value: '',
      required: false,
      icon: Zap
    }
  ])

  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [isExpanded, setIsExpanded] = useState(true)
  const builderRef = useRef<HTMLDivElement>(null)

  const templates = {
    'technical-analysis': {
      name: 'Technical Analysis',
      sections: {
        role: 'You are a senior technical analyst with expertise in software architecture and system design.',
        context: 'We are evaluating different technical approaches for a new project.',
        task: 'Analyze the technical requirements and provide recommendations for the best approach.',
        constraints: 'Consider scalability, maintainability, cost, and team expertise.',
        output: 'Provide a structured analysis with pros/cons, recommendations, and implementation timeline.'
      }
    },
    'creative-writing': {
      name: 'Creative Writing Assistant',
      sections: {
        role: 'You are a creative writing mentor specializing in narrative development.',
        context: 'The writer is working on a science fiction novel.',
        task: 'Help develop compelling characters and plot elements.',
        constraints: 'Keep the tone consistent with hard science fiction genre.',
        output: 'Provide detailed character profiles and plot suggestions with examples.'
      }
    },
    'business-strategy': {
      name: 'Business Strategy',
      sections: {
        role: 'You are a business strategy consultant with MBA-level expertise.',
        context: 'A startup is looking to expand into new markets.',
        task: 'Develop a comprehensive market entry strategy.',
        constraints: 'Limited budget of $500k, 12-month timeline.',
        output: 'Create a detailed strategy document with market analysis, timeline, and budget breakdown.'
      }
    }
  }

  useEffect(() => {
    if (builderRef.current) {
      gsap.fromTo(
        '.prompt-section',
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.5, stagger: 0.1, ease: 'power3.out' }
      )
    }
  }, [])

  const updateSection = (id: string, value: string) => {
    setSections(prev => prev.map(section => 
      section.id === id ? { ...section, value } : section
    ))
  }

  const applyTemplate = (templateKey: string) => {
    const template = templates[templateKey as keyof typeof templates]
    if (template) {
      setSections(prev => prev.map(section => ({
        ...section,
        value: template.sections[section.id as keyof typeof template.sections] || ''
      })))
      setSelectedTemplate(templateKey)
    }
  }

  const clearAll = () => {
    setSections(prev => prev.map(section => ({ ...section, value: '' })))
    setSelectedTemplate('')
  }

  const generatePrompt = () => {
    const filledSections = sections.filter(section => section.value.trim())
    return filledSections.map(section => section.value.trim()).join('\n\n')
  }

  const generatedPrompt = generatePrompt()

  return (
    <div ref={builderRef} className="card p-6 lg:p-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
            <Lightbulb className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Interactive Prompt Builder</h3>
            <p className="text-sm text-slate-400">Build effective prompts step by step</p>
          </div>
        </div>
        
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-2 text-slate-400 hover:text-white transition-colors"
        >
          {isExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
        </button>
      </div>

      {isExpanded && (
        <>
          {/* Templates */}
          <div className="mb-6">
            <h4 className="text-lg font-medium text-white mb-3">Quick Start Templates</h4>
            <div className="flex flex-wrap gap-2 mb-4">
              {Object.entries(templates).map(([key, template]) => (
                <button
                  key={key}
                  onClick={() => applyTemplate(key)}
                  className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                    selectedTemplate === key
                      ? 'bg-primary-600 text-white'
                      : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                  }`}
                >
                  {template.name}
                </button>
              ))}
              <button
                onClick={clearAll}
                className="px-3 py-2 bg-slate-700 text-slate-300 hover:bg-slate-600 rounded-lg text-sm transition-colors flex items-center space-x-1"
              >
                <RefreshCw className="w-3 h-3" />
                <span>Clear All</span>
              </button>
            </div>
          </div>

          {/* Prompt Sections */}
          <div className="space-y-6 mb-8">
            {sections.map((section) => {
              const Icon = section.icon
              return (
                <div key={section.id} className="prompt-section">
                  <div className="flex items-start space-x-3 mb-3">
                    <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                      <Icon className="w-4 h-4 text-slate-300" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="font-medium text-white">{section.title}</h5>
                        {section.required && (
                          <span className="text-xs text-red-400">*</span>
                        )}
                      </div>
                      <p className="text-sm text-slate-400 mb-3">{section.description}</p>
                      <textarea
                        value={section.value}
                        onChange={(e) => updateSection(section.id, e.target.value)}
                        placeholder={section.placeholder}
                        className="w-full h-24 px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                      />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* Generated Prompt */}
          {generatedPrompt && (
            <div className="border-t border-slate-700/50 pt-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-medium text-white">Generated Prompt</h4>
                <div className="text-sm text-slate-400">
                  {generatedPrompt.length} characters
                </div>
              </div>
              
              <CodeBlock
                code={generatedPrompt}
                language="text"
                title="Your Custom Prompt"
                copyable={true}
                showLineNumbers={false}
              />
              
              <div className="mt-4 p-4 bg-primary-500/10 border border-primary-500/20 rounded-lg">
                <p className="text-sm text-primary-300">
                  <strong>Tip:</strong> Copy this prompt and test it with your preferred AI system. 
                  Adjust the sections above to refine the output quality.
                </p>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default PromptBuilder
