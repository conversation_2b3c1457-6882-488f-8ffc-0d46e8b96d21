<testsuites id="" name="" tests="63" failures="6" skipped="55" errors="0" time="70.806922">
<testsuite name="homepage.spec.ts" timestamp="2025-06-26T03:53:39.731Z" hostname="chromium" tests="9" failures="6" skipped="1" time="98.086" errors="0">
<testcase name="Homepage › should load homepage successfully" classname="homepage.spec.ts" time="10.998">
<failure message="homepage.spec.ts:8:3 should load homepage successfully" type="FAILURE">
<![CDATA[  [chromium] › homepage.spec.ts:8:3 › Homepage › should load homepage successfully ─────────────────

    Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:
        1) <h1 class="text-xl font-bold gradient-text">AI Prompts Mastery</h1> aka getByRole('link', { name: 'AI Prompts Mastery Guide Home' })
        2) <h1 class="hero-title heading-1 mb-6">…</h1> aka getByRole('heading', { name: 'AI System Prompts Mastery' })

    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')


      11 |     
      12 |     // Check main heading
    > 13 |     await expect(page.locator('h1')).toContainText('AI System Prompts')
         |                                      ^
      14 |     
      15 |     // Check hero section
      16 |     await expect(page.locator('.hero-title')).toBeVisible()
        at E:\Augment Code Testing\ai-prompts-mastery-website\tests\homepage.spec.ts:13:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-load-homepage-successfully-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-load-homepage-successfully-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\homepage-Homepage-should-load-homepage-successfully-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|homepage-Homepage-should-load-homepage-successfully-chromium\test-failed-1.png]]

[[ATTACHMENT|homepage-Homepage-should-load-homepage-successfully-chromium\video.webm]]

[[ATTACHMENT|homepage-Homepage-should-load-homepage-successfully-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Homepage › should have working navigation" classname="homepage.spec.ts" time="10.615">
<failure message="homepage.spec.ts:21:3 should have working navigation" type="FAILURE">
<![CDATA[  [chromium] › homepage.spec.ts:21:3 › Homepage › should have working navigation ───────────────────

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=Home') resolved to 2 elements:
        1) <a href="/" class="nav-link text-sm font-medium">Home</a> aka getByRole('banner').getByRole('link', { name: 'Home', exact: true })
        2) <span class="text-sm lg:text-base font-medium">Home</span> aka getByText('HomeWelcome & Overview')

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Home')


      36 |     
      37 |     for (const item of navItems) {
    > 38 |       await expect(page.locator(`text=${item}`)).toBeVisible()
         |                                                  ^
      39 |     }
      40 |   })
      41 |
        at E:\Augment Code Testing\ai-prompts-mastery-website\tests\homepage.spec.ts:38:50

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-working-navigation-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-working-navigation-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\homepage-Homepage-should-have-working-navigation-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|homepage-Homepage-should-have-working-navigation-chromium\test-failed-1.png]]

[[ATTACHMENT|homepage-Homepage-should-have-working-navigation-chromium\video.webm]]

[[ATTACHMENT|homepage-Homepage-should-have-working-navigation-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Homepage › should have responsive design" classname="homepage.spec.ts" time="10.651">
<failure message="homepage.spec.ts:42:3 should have responsive design" type="FAILURE">
<![CDATA[  [chromium] › homepage.spec.ts:42:3 › Homepage › should have responsive design ────────────────────

    Error: expect.toBeVisible: Error: strict mode violation: locator('.container') resolved to 4 elements:
        1) <div class="header-content container mx-auto px-4 sm:px-6 lg:px-8">…</div> aka locator('div').filter({ hasText: 'AI Prompts MasteryInteractive GuideHomeOwner\'s ManualTechnical SpecsOperating' }).nth(2)
        2) <div class="container mx-auto px-4 sm:px-6 lg:px-8">…</div> aka locator('div').filter({ hasText: 'HomeWelcome & OverviewOwner\'s' }).nth(2)
        3) <div class="relative container mx-auto px-4 sm:px-6 lg:px-8 text-center">…</div> aka getByText('AI System PromptsMastery GuideThe Ultimate Interactive ManualTransform from')
        4) <div class="container mx-auto px-4 sm:px-6 lg:px-8">…</div> aka locator('#root div').filter({ hasText: 'AI Prompts MasteryInteractive GuideThe ultimate interactive guide to mastering' }).nth(1)

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.container')


      43 |     // Test desktop view
      44 |     await page.setViewportSize({ width: 1200, height: 800 })
    > 45 |     await expect(page.locator('.container')).toBeVisible()
         |                                              ^
      46 |     
      47 |     // Test tablet view
      48 |     await page.setViewportSize({ width: 768, height: 1024 })
        at E:\Augment Code Testing\ai-prompts-mastery-website\tests\homepage.spec.ts:45:46

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-responsive-design-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-responsive-design-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\homepage-Homepage-should-have-responsive-design-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|homepage-Homepage-should-have-responsive-design-chromium\test-failed-1.png]]

[[ATTACHMENT|homepage-Homepage-should-have-responsive-design-chromium\video.webm]]

[[ATTACHMENT|homepage-Homepage-should-have-responsive-design-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Homepage › should have working CTA buttons" classname="homepage.spec.ts" time="13.305">
<failure message="homepage.spec.ts:59:3 should have working CTA buttons" type="FAILURE">
<![CDATA[  [chromium] › homepage.spec.ts:59:3 › Homepage › should have working CTA buttons ──────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveAttribute(expected)

    Locator: locator('text=Start Learning')
    Expected string: "/section/owners-manual"
    Received string: ""
    Call log:
      - Expect "toHaveAttribute" with timeout 5000ms
      - waiting for locator('text=Start Learning')
        8 × locator resolved to <span>Start Learning</span>
          - unexpected value "null"


      61 |     const startButton = page.locator('text=Start Learning')
      62 |     await expect(startButton).toBeVisible()
    > 63 |     await expect(startButton).toHaveAttribute('href', '/section/owners-manual')
         |                               ^
      64 |     
      65 |     // Check "Advanced Techniques" button
      66 |     const advancedButton = page.locator('text=Advanced Techniques')
        at E:\Augment Code Testing\ai-prompts-mastery-website\tests\homepage.spec.ts:63:31

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-working-CTA-buttons-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-working-CTA-buttons-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\homepage-Homepage-should-have-working-CTA-buttons-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|homepage-Homepage-should-have-working-CTA-buttons-chromium\test-failed-1.png]]

[[ATTACHMENT|homepage-Homepage-should-have-working-CTA-buttons-chromium\video.webm]]

[[ATTACHMENT|homepage-Homepage-should-have-working-CTA-buttons-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Homepage › should have features section" classname="homepage.spec.ts" time="13.106">
</testcase>
<testcase name="Homepage › should have guide sections" classname="homepage.spec.ts" time="10.102">
</testcase>
<testcase name="Homepage › should have working scroll progress" classname="homepage.spec.ts" time="8.567">
<failure message="homepage.spec.ts:108:3 should have working scroll progress" type="FAILURE">
<![CDATA[  [chromium] › homepage.spec.ts:108:3 › Homepage › should have working scroll progress ─────────────

    Error: expect.toBeVisible: Error: strict mode violation: locator('.h-full.bg-gradient-to-r') resolved to 2 elements:
        1) <div class="h-full bg-gradient-to-r from-primary-500 to-accent-500 transition-all duration-300"></div> aka locator('.h-full').first()
        2) <div class="h-full bg-gradient-to-r from-primary-500 via-accent-500 to-primary-600 transition-all duration-150 ease-out"></div> aka locator('.h-full.bg-gradient-to-r.from-primary-500.via-accent-500')

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.h-full.bg-gradient-to-r')


      116 |     // Check that progress bar has some width
      117 |     const progressBar = page.locator('.h-full.bg-gradient-to-r')
    > 118 |     await expect(progressBar).toBeVisible()
          |                               ^
      119 |   })
      120 |
      121 |   test('should have accessibility features', async ({ page }) => {
        at E:\Augment Code Testing\ai-prompts-mastery-website\tests\homepage.spec.ts:118:31

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-working-scroll-progress-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-working-scroll-progress-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\homepage-Homepage-should-have-working-scroll-progress-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|homepage-Homepage-should-have-working-scroll-progress-chromium\test-failed-1.png]]

[[ATTACHMENT|homepage-Homepage-should-have-working-scroll-progress-chromium\video.webm]]

[[ATTACHMENT|homepage-Homepage-should-have-working-scroll-progress-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Homepage › should have accessibility features" classname="homepage.spec.ts" time="13.262">
<failure message="homepage.spec.ts:121:3 should have accessibility features" type="FAILURE">
<![CDATA[  [chromium] › homepage.spec.ts:121:3 › Homepage › should have accessibility features ──────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeInViewport()

    Locator: locator('text=Skip to main content')
    Expected: in viewport
    Received: viewport ratio 0
    Call log:
      - Expect "toBeInViewport" with timeout 5000ms
      - waiting for locator('text=Skip to main content')
        8 × locator resolved to <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-primary-600 text-white px-4 py-2 rounded-lg">Skip to main content</a>
          - unexpected value "viewport ratio 0"


      121 |   test('should have accessibility features', async ({ page }) => {
      122 |     // Check skip link
    > 123 |     await expect(page.locator('text=Skip to main content')).toBeInViewport({ ratio: 0 })
          |                                                             ^
      124 |     
      125 |     // Check main content has proper ID
      126 |     await expect(page.locator('#main-content')).toBeVisible()
        at E:\Augment Code Testing\ai-prompts-mastery-website\tests\homepage.spec.ts:123:61

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-accessibility-features-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\homepage-Homepage-should-have-accessibility-features-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\homepage-Homepage-should-have-accessibility-features-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|homepage-Homepage-should-have-accessibility-features-chromium\test-failed-1.png]]

[[ATTACHMENT|homepage-Homepage-should-have-accessibility-features-chromium\video.webm]]

[[ATTACHMENT|homepage-Homepage-should-have-accessibility-features-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Homepage › should handle mobile menu" classname="homepage.spec.ts" time="7.48">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="homepage.spec.ts" timestamp="2025-06-26T03:53:39.731Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Homepage › should load homepage successfully" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working navigation" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have responsive design" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working CTA buttons" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have features section" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have guide sections" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working scroll progress" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have accessibility features" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should handle mobile menu" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="homepage.spec.ts" timestamp="2025-06-26T03:53:39.731Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Homepage › should load homepage successfully" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working navigation" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have responsive design" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working CTA buttons" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have features section" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have guide sections" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working scroll progress" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have accessibility features" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should handle mobile menu" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="homepage.spec.ts" timestamp="2025-06-26T03:53:39.731Z" hostname="Mobile Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Homepage › should load homepage successfully" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working navigation" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have responsive design" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working CTA buttons" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have features section" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have guide sections" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working scroll progress" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have accessibility features" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should handle mobile menu" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="homepage.spec.ts" timestamp="2025-06-26T03:53:39.731Z" hostname="Mobile Safari" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Homepage › should load homepage successfully" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working navigation" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have responsive design" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working CTA buttons" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have features section" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have guide sections" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working scroll progress" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have accessibility features" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should handle mobile menu" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="homepage.spec.ts" timestamp="2025-06-26T03:53:39.731Z" hostname="Microsoft Edge" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Homepage › should load homepage successfully" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working navigation" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have responsive design" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working CTA buttons" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have features section" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have guide sections" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working scroll progress" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have accessibility features" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should handle mobile menu" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="homepage.spec.ts" timestamp="2025-06-26T03:53:39.731Z" hostname="Google Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Homepage › should load homepage successfully" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working navigation" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have responsive design" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working CTA buttons" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have features section" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have guide sections" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have working scroll progress" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should have accessibility features" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Homepage › should handle mobile menu" classname="homepage.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>