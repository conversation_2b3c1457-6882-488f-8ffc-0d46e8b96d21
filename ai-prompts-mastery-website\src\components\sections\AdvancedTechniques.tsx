import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import {
  Zap,
  Brain,
  GitBranch,
  Shield,
  Users,
  Layers,
  CheckCircle,
  ArrowRight,
  Lightbulb,
  Target,
  Cpu,
  Network
} from 'lucide-react'
import CodeBlock from '../ui/CodeBlock'

const AdvancedTechniques: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const techniquesRef = useRef<HTMLDivElement>(null)
  const examplesRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Animate section entrance
    if (sectionRef.current) {
      gsap.fromTo(
        '.section-header',
        { y: 80, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: 'power3.out' }
      )
    }

    // Animate technique cards
    if (techniquesRef.current) {
      gsap.fromTo(
        '.technique-card',
        { y: 60, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: techniquesRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    // Animate examples
    if (examplesRef.current) {
      gsap.fromTo(
        '.example-card',
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: examplesRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const advancedTechniques = [
    {
      title: 'Tree of Thoughts (ToT)',
      description: 'Explore multiple reasoning paths simultaneously for complex problem-solving',
      icon: GitBranch,
      color: 'from-blue-500 to-cyan-500',
      difficulty: 'Expert',
      useCase: 'Complex analysis, strategic planning, creative problem-solving',
      template: `Let's explore this problem using multiple approaches:

Path A: [APPROACH 1]
- Step 1: [REASONING]
- Step 2: [REASONING]
- Evaluation: [PROS/CONS]

Path B: [APPROACH 2]
- Step 1: [REASONING]
- Step 2: [REASONING]
- Evaluation: [PROS/CONS]

Path C: [APPROACH 3]
- Step 1: [REASONING]
- Step 2: [REASONING]
- Evaluation: [PROS/CONS]

Now synthesize the best elements from each path to create an optimal solution.`,
      benefits: [
        'Explores multiple solution paths',
        'Reduces cognitive bias',
        'Improves solution quality',
        'Provides comprehensive analysis'
      ]
    },
    {
      title: 'Metacognitive Prompting',
      description: 'Teaching AI to think about its own thinking process',
      icon: Brain,
      color: 'from-purple-500 to-pink-500',
      difficulty: 'Advanced',
      useCase: 'Quality assurance, self-correction, confidence assessment',
      template: `Before answering, reflect on:
1. What type of problem is this?
2. What knowledge domains are relevant?
3. What are the potential pitfalls in reasoning about this?
4. How confident should I be in different aspects of my response?
5. What would I need to know to give a better answer?

Then provide your response with explicit confidence levels for different claims.`,
      benefits: [
        'Improves response quality',
        'Provides confidence indicators',
        'Reduces hallucinations',
        'Enables self-correction'
      ]
    },
    {
      title: 'Constitutional AI Pattern',
      description: 'Implement safety and alignment through constitutional principles',
      icon: Shield,
      color: 'from-green-500 to-emerald-500',
      difficulty: 'Advanced',
      useCase: 'Safety-critical applications, ethical AI, content moderation',
      template: `<primary_objective>
Your main goal is to [OBJECTIVE]
</primary_objective>

<constraints>
- You must [REQUIREMENT 1]
- You cannot [RESTRICTION 1]
- You should [GUIDELINE 1]
</constraints>

<evaluation_criteria>
Before responding, consider:
1. Does this meet the objective?
2. Does this violate any constraints?
3. Is this helpful and harmless?
</evaluation_criteria>`,
      benefits: [
        'Ensures ethical behavior',
        'Provides safety guardrails',
        'Enables value alignment',
        'Reduces harmful outputs'
      ]
    },
    {
      title: 'Multi-Agent Simulation',
      description: 'Simulate team discussions with multiple AI perspectives',
      icon: Users,
      color: 'from-orange-500 to-red-500',
      difficulty: 'Expert',
      useCase: 'Team decision-making, comprehensive analysis, stakeholder perspectives',
      template: `Simulate a team discussion with these roles:
- Project Manager: Focuses on timeline and resources
- Technical Lead: Considers implementation challenges
- UX Designer: Prioritizes user experience
- QA Engineer: Identifies potential issues

Have each role contribute their perspective on [PROBLEM], then synthesize into a comprehensive solution.`,
      benefits: [
        'Multiple perspectives',
        'Comprehensive analysis',
        'Identifies blind spots',
        'Simulates real team dynamics'
      ]
    },
    {
      title: 'Dynamic Context Adaptation',
      description: 'Adapt responses based on user characteristics and context',
      icon: Layers,
      color: 'from-teal-500 to-cyan-500',
      difficulty: 'Advanced',
      useCase: 'Personalized responses, educational content, adaptive interfaces',
      template: `Adapt your response style based on these user characteristics:
- Expertise level: [BEGINNER/INTERMEDIATE/EXPERT]
- Communication preference: [CONCISE/DETAILED/CONVERSATIONAL]
- Goal: [LEARNING/PROBLEM_SOLVING/CREATIVE_EXPLORATION]
- Time constraint: [URGENT/MODERATE/FLEXIBLE]

Adjust your language, depth, and examples accordingly.`,
      benefits: [
        'Personalized responses',
        'Better user experience',
        'Improved comprehension',
        'Context-aware adaptation'
      ]
    },
    {
      title: 'Constraint-Based Creativity',
      description: 'Use limitations to spark more creative solutions',
      icon: Target,
      color: 'from-pink-500 to-purple-500',
      difficulty: 'Intermediate',
      useCase: 'Creative writing, design challenges, innovation workshops',
      template: `Create [CREATIVE OUTPUT] with these constraints:
- Must include [REQUIRED ELEMENT 1]
- Cannot use [FORBIDDEN ELEMENT]
- Should evoke [EMOTIONAL RESPONSE]
- Limited to [CONSTRAINT] words/elements
- Must incorporate [UNUSUAL REQUIREMENT]

Constraints often spark more creative solutions than complete freedom.`,
      benefits: [
        'Enhances creativity',
        'Forces innovative thinking',
        'Provides clear boundaries',
        'Generates unique solutions'
      ]
    }
  ]

  const practicalExamples = [
    {
      title: 'Business Strategy with Tree of Thoughts',
      scenario: 'Developing a market entry strategy for a new product',
      technique: 'Tree of Thoughts',
      icon: Cpu,
      prompt: `Let's explore market entry strategies using multiple approaches:

Path A: Direct Competition Approach
- Step 1: Analyze competitor weaknesses and market gaps
- Step 2: Position product as superior alternative
- Step 3: Aggressive pricing and marketing campaign
- Evaluation: High risk, high reward, requires significant investment

Path B: Niche Market Approach
- Step 1: Identify underserved market segments
- Step 2: Tailor product features for specific needs
- Step 3: Build strong relationships in niche community
- Evaluation: Lower risk, steady growth, limited initial scale

Path C: Partnership Approach
- Step 1: Identify potential strategic partners
- Step 2: Develop mutually beneficial partnership models
- Step 3: Leverage partner networks for market access
- Evaluation: Shared risk, faster market access, dependency concerns

Synthesize the best elements: Start with niche market validation (Path B), build partnerships for distribution (Path C), then scale with competitive positioning (Path A).`,
      outcome: 'Comprehensive strategy that minimizes risk while maximizing growth potential'
    },
    {
      title: 'Technical Architecture with Metacognitive Prompting',
      scenario: 'Designing a scalable microservices architecture',
      technique: 'Metacognitive Prompting',
      icon: Network,
      prompt: `Before designing the architecture, let me reflect on:

1. Problem type: This is a complex system design problem requiring trade-offs between scalability, maintainability, and performance.

2. Relevant domains: Distributed systems, cloud architecture, DevOps, data consistency, service communication patterns.

3. Potential pitfalls: Over-engineering, premature optimization, ignoring operational complexity, underestimating data consistency challenges.

4. Confidence levels:
   - High confidence: Basic microservices patterns, containerization strategies
   - Medium confidence: Specific technology choices, scaling thresholds
   - Low confidence: Long-term maintenance costs, team adoption challenges

5. Additional information needed: Team size and expertise, expected load patterns, compliance requirements, budget constraints.

Based on this reflection, here's my architecture recommendation with explicit confidence indicators for each component...`,
      outcome: 'More thoughtful architecture with clear confidence levels and identified knowledge gaps'
    }
  ]

  return (
    <section ref={sectionRef} id="advanced-techniques" className="section">
      <div className="section-container">
        {/* Header */}
        <div className="section-header text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl mb-6">
            <Zap className="w-8 h-8 text-white" />
          </div>
          <h1 className="heading-1 mb-6 text-white">
            Advanced Techniques
          </h1>
          <p className="body-large max-w-3xl mx-auto mb-8">
            Creative applications beyond standard usage patterns. Master cutting-edge
            techniques for power users and professionals seeking maximum AI potential.
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-400">
            <CheckCircle className="w-4 h-4 text-yellow-500" />
            <span>Expert Level</span>
            <span>•</span>
            <span>30 min read</span>
            <span>•</span>
            <span>Cutting-Edge Techniques</span>
          </div>
        </div>

        {/* Advanced Techniques */}
        <div ref={techniquesRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Cutting-Edge Prompt Engineering Techniques
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Master these advanced techniques to unlock the full potential of AI systems
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {advancedTechniques.map((technique, index) => {
              const Icon = technique.icon
              return (
                <div key={index} className="technique-card card card-hover p-6 group">
                  <div className="flex items-start space-x-4 mb-6">
                    <div className={`w-12 h-12 bg-gradient-to-br ${technique.color} rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-xl font-semibold text-white">
                          {technique.title}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          technique.difficulty === 'Expert'
                            ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                            : technique.difficulty === 'Advanced'
                            ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30'
                            : 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                        }`}>
                          {technique.difficulty}
                        </span>
                      </div>
                      <p className="text-slate-400 mb-4 leading-relaxed">
                        {technique.description}
                      </p>
                      <div className="text-sm text-slate-500 mb-4">
                        <strong>Best for:</strong> {technique.useCase}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-slate-300 mb-3 flex items-center">
                        <Lightbulb className="w-4 h-4 mr-2 text-primary-400" />
                        Template
                      </h4>
                      <CodeBlock
                        code={technique.template}
                        language="text"
                        showLineNumbers={false}
                        copyable={true}
                        className="text-xs"
                      />
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-slate-300 mb-3">Key Benefits</h4>
                      <div className="grid grid-cols-2 gap-2">
                        {technique.benefits.map((benefit, idx) => (
                          <div key={idx} className="flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-primary-500 rounded-full flex-shrink-0"></div>
                            <span className="text-xs text-slate-400">{benefit}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Practical Examples */}
        <div ref={examplesRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Real-World Applications
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              See these advanced techniques in action with practical business scenarios
            </p>
          </div>

          <div className="space-y-8">
            {practicalExamples.map((example, index) => {
              const Icon = example.icon
              return (
                <div key={index} className="example-card card p-8">
                  <div className="flex items-start space-x-6 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-xl font-semibold text-white">
                          {example.title}
                        </h3>
                        <span className="px-3 py-1 bg-primary-500/20 text-primary-400 border border-primary-500/30 rounded-full text-sm font-medium">
                          {example.technique}
                        </span>
                      </div>
                      <p className="text-slate-400 mb-4">
                        <strong>Scenario:</strong> {example.scenario}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2">
                      <h4 className="text-lg font-medium text-white mb-3">Complete Prompt Example</h4>
                      <CodeBlock
                        code={example.prompt}
                        language="text"
                        title={`${example.technique} Example`}
                        showLineNumbers={false}
                        copyable={true}
                      />
                    </div>

                    <div>
                      <h4 className="text-lg font-medium text-white mb-3">Expected Outcome</h4>
                      <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                        <p className="text-sm text-green-300 leading-relaxed">
                          {example.outcome}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Next Steps */}
        <div className="text-center">
          <div className="card p-8 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border border-yellow-500/20">
            <h3 className="text-2xl font-semibold text-white mb-4">
              Need Help Debugging Your Prompts?
            </h3>
            <p className="body-normal mb-6 max-w-2xl mx-auto">
              Even advanced techniques can run into issues. Learn how to troubleshoot
              and fix common problems with our comprehensive debugging guide.
            </p>
            <a
              href="/section/troubleshooting"
              className="btn btn-primary px-8 py-3 text-lg font-semibold inline-flex items-center group"
            >
              <span>Troubleshooting Guide</span>
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AdvancedTechniques
