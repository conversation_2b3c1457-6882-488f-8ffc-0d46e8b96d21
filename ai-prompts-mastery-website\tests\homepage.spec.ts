import { test, expect } from '@playwright/test'

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should load homepage successfully', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/AI System Prompts Mastery Guide/)
    
    // Check main heading
    await expect(page.locator('h1')).toContainText('AI System Prompts')
    
    // Check hero section
    await expect(page.locator('.hero-title')).toBeVisible()
    await expect(page.locator('.hero-subtitle')).toBeVisible()
    await expect(page.locator('.hero-description')).toBeVisible()
  })

  test('should have working navigation', async ({ page }) => {
    // Check navigation is visible
    await expect(page.locator('nav[role="navigation"]')).toBeVisible()
    
    // Check navigation items
    const navItems = [
      'Home',
      "Owner's Manual",
      'Technical Specs',
      'Operating Instructions',
      'Advanced Techniques',
      'Troubleshooting',
      'Maintenance',
      'Safety & Compliance'
    ]
    
    for (const item of navItems) {
      await expect(page.locator(`text=${item}`)).toBeVisible()
    }
  })

  test('should have responsive design', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('.container')).toBeVisible()
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(page.locator('.container')).toBeVisible()
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('.container')).toBeVisible()
    
    // Check mobile menu button is visible on mobile
    await expect(page.locator('button[aria-label="Toggle navigation menu"]')).toBeVisible()
  })

  test('should have working CTA buttons', async ({ page }) => {
    // Check "Start Learning" button
    const startButton = page.locator('text=Start Learning')
    await expect(startButton).toBeVisible()
    await expect(startButton).toHaveAttribute('href', '/section/owners-manual')
    
    // Check "Advanced Techniques" button
    const advancedButton = page.locator('text=Advanced Techniques')
    await expect(advancedButton).toBeVisible()
    await expect(advancedButton).toHaveAttribute('href', '/section/advanced-techniques')
  })

  test('should have features section', async ({ page }) => {
    // Check features section is visible
    await expect(page.locator('text=Why This Guide is Different')).toBeVisible()
    
    // Check feature cards
    const features = [
      'Expert Analysis',
      'Interactive Learning',
      'Advanced Techniques',
      'Production Ready'
    ]
    
    for (const feature of features) {
      await expect(page.locator(`.feature-card:has-text("${feature}")`)).toBeVisible()
    }
  })

  test('should have guide sections', async ({ page }) => {
    // Check guide sections heading
    await expect(page.locator('text=Complete Learning Path')).toBeVisible()
    
    // Check section cards
    const sections = [
      "Owner's Manual",
      'Technical Specifications',
      'Operating Instructions',
      'Advanced Techniques',
      'Troubleshooting Guide',
      'Maintenance & Optimization',
      'Safety & Compliance'
    ]
    
    for (const section of sections) {
      await expect(page.locator(`.section-card:has-text("${section}")`)).toBeVisible()
    }
  })

  test('should have working scroll progress', async ({ page }) => {
    // Check scroll progress indicator exists
    await expect(page.locator('.fixed.top-0.left-0.right-0.z-50.h-1')).toBeVisible()
    
    // Scroll down and check progress updates
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight / 2))
    await page.waitForTimeout(500)
    
    // Check that progress bar has some width
    const progressBar = page.locator('.h-full.bg-gradient-to-r')
    await expect(progressBar).toBeVisible()
  })

  test('should have accessibility features', async ({ page }) => {
    // Check skip link
    await expect(page.locator('text=Skip to main content')).toBeInViewport({ ratio: 0 })
    
    // Check main content has proper ID
    await expect(page.locator('#main-content')).toBeVisible()
    
    // Check navigation has proper ARIA labels
    await expect(page.locator('nav[aria-label="Main navigation"]')).toBeVisible()
    
    // Check buttons have proper labels
    await expect(page.locator('button[aria-label="Toggle navigation menu"]')).toBeVisible()
  })

  test('should handle mobile menu', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check mobile menu button
    const menuButton = page.locator('button[aria-label="Toggle navigation menu"]')
    await expect(menuButton).toBeVisible()
    
    // Click to open menu
    await menuButton.click()
    
    // Check menu is expanded
    await expect(menuButton).toHaveAttribute('aria-expanded', 'true')
    
    // Check navigation items are visible
    await expect(page.locator('text=Owner\'s Manual')).toBeVisible()
    
    // Click to close menu
    await menuButton.click()
    
    // Check menu is collapsed
    await expect(menuButton).toHaveAttribute('aria-expanded', 'false')
  })
})
