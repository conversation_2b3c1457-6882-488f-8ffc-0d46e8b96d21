import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import {
  PlayCircle,
  User,
  Target,
  Link,
  CheckCircle,
  ArrowRight,
  Code,
  MessageSquare,
  Lightbulb,
  FileText
} from 'lucide-react'
import CodeBlock from '../ui/CodeBlock'
import PromptBuilder from '../interactive/PromptBuilder'

const OperatingInstructions: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const patternsRef = useRef<HTMLDivElement>(null)
  const examplesRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Animate section entrance
    if (sectionRef.current) {
      gsap.fromTo(
        '.section-header',
        { y: 80, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: 'power3.out' }
      )
    }

    // Animate pattern cards
    if (patternsRef.current) {
      gsap.fromTo(
        '.pattern-card',
        { y: 60, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: patternsRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    // Animate examples
    if (examplesRef.current) {
      gsap.fromTo(
        '.example-card',
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: examplesRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      )
    }

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const basicPatterns = [
    {
      title: 'Role-Based Prompting',
      description: 'Assign a specific role or expertise to the AI',
      icon: User,
      color: 'from-blue-500 to-cyan-500',
      template: `You are a [SPECIFIC ROLE] with [EXPERTISE AREAS].
Your goal is to [PRIMARY OBJECTIVE].
You should [BEHAVIORAL GUIDELINES].`,
      example: `You are a senior software architect with 15 years of experience in distributed systems.
Your goal is to help design scalable, maintainable software solutions.
You should provide concrete examples, consider trade-offs, and ask clarifying questions about requirements.`
    },
    {
      title: 'Task-Specific Prompting',
      description: 'Structure prompts around specific tasks and objectives',
      icon: Target,
      color: 'from-green-500 to-emerald-500',
      template: `Task: [CLEAR OBJECTIVE]
Context: [RELEVANT BACKGROUND]
Requirements: [SPECIFIC NEEDS]
Output Format: [DESIRED STRUCTURE]`,
      example: `Task: Create a marketing strategy for a new SaaS product
Context: B2B productivity tool for remote teams, $50/month pricing
Requirements: Target 1000 customers in 6 months, limited budget
Output Format: Detailed plan with timeline, channels, and budget breakdown`
    },
    {
      title: 'Chain-of-Thought Prompting',
      description: 'Guide the AI through step-by-step reasoning',
      icon: Link,
      color: 'from-purple-500 to-pink-500',
      template: `Let's work through this step by step:
1. First, [INITIAL STEP]
2. Then, [SUBSEQUENT STEP]
3. Finally, [CONCLUSION STEP]

Show your reasoning for each step.`,
      example: `Let's work through this step by step:
1. First, analyze the current market conditions and competitors
2. Then, identify our unique value proposition and target audience
3. Finally, develop specific tactics and success metrics

Show your reasoning for each step.`
    }
  ]

  const useCases = [
    {
      title: 'Creative Writing Assistant',
      description: 'Help with storytelling, character development, and narrative structure',
      icon: FileText,
      prompt: `You are a creative writing mentor specializing in [GENRE].
For each piece of writing:
1. Analyze narrative structure and pacing
2. Evaluate character development and dialogue
3. Suggest improvements for [SPECIFIC ASPECT]
4. Provide examples of effective techniques
5. Encourage experimentation while maintaining quality

Style: Encouraging but honest, specific rather than generic`,
      tips: [
        'Specify the genre for targeted advice',
        'Include specific aspects you want feedback on',
        'Ask for examples from published works',
        'Request both strengths and areas for improvement'
      ]
    },
    {
      title: 'Technical Documentation Generator',
      description: 'Create clear, comprehensive technical documentation',
      icon: Code,
      prompt: `You are a technical documentation specialist.
For each topic:
1. Start with a clear overview
2. Break down complex concepts into digestible sections
3. Include practical examples and code snippets
4. Add troubleshooting sections for common issues
5. Conclude with next steps or related topics

Format: Use markdown with proper headers, code blocks, and lists`,
      tips: [
        'Specify the target audience (beginners, experts, etc.)',
        'Include the technology stack or tools involved',
        'Request specific formatting requirements',
        'Ask for troubleshooting sections'
      ]
    },
    {
      title: 'Data Analysis Assistant',
      description: 'Analyze data and provide actionable insights',
      icon: MessageSquare,
      prompt: `You are a data scientist with expertise in [DOMAIN].
For each analysis:
1. Understand the business context and objectives
2. Examine data quality and limitations
3. Apply appropriate statistical methods
4. Visualize findings clearly
5. Translate results into actionable insights

Always explain your methodology and assumptions.`,
      tips: [
        'Provide context about the business problem',
        'Specify the type of data and format',
        'Include any constraints or requirements',
        'Ask for specific visualization types'
      ]
    }
  ]

  return (
    <section ref={sectionRef} id="operating-instructions" className="section">
      <div className="section-container">
        {/* Header */}
        <div className="section-header text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl mb-6">
            <PlayCircle className="w-8 h-8 text-white" />
          </div>
          <h1 className="heading-1 mb-6 text-white">
            Operating Instructions
          </h1>
          <p className="body-large max-w-3xl mx-auto mb-8">
            Step-by-step guides for different use cases and scenarios. Learn how to
            effectively operate AI systems for various tasks with proven patterns.
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-400">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>Practical Guide</span>
            <span>•</span>
            <span>20 min read</span>
            <span>•</span>
            <span>Step-by-Step Instructions</span>
          </div>
        </div>

        {/* Basic Prompt Engineering Patterns */}
        <div ref={patternsRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Basic Prompt Engineering Patterns
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Master these fundamental patterns to create effective prompts for any AI system
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {basicPatterns.map((pattern, index) => {
              const Icon = pattern.icon
              return (
                <div key={index} className="pattern-card card card-hover p-6 group">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className={`w-10 h-10 bg-gradient-to-br ${pattern.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">
                      {pattern.title}
                    </h3>
                  </div>

                  <p className="text-slate-400 mb-6 leading-relaxed">
                    {pattern.description}
                  </p>

                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-slate-300 mb-2">Template:</h4>
                      <CodeBlock
                        code={pattern.template}
                        language="text"
                        showLineNumbers={false}
                        copyable={true}
                        className="text-xs"
                      />
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-slate-300 mb-2">Example:</h4>
                      <CodeBlock
                        code={pattern.example}
                        language="text"
                        showLineNumbers={false}
                        copyable={true}
                        className="text-xs"
                      />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Interactive Prompt Builder */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Interactive Prompt Builder
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Build effective prompts step by step with our interactive tool
            </p>
          </div>

          <PromptBuilder />
        </div>

        {/* Specialized Use Cases */}
        <div ref={examplesRef} className="mb-20">
          <div className="text-center mb-12">
            <h2 className="heading-2 text-white mb-4">
              Specialized Use Cases
            </h2>
            <p className="body-large max-w-3xl mx-auto">
              Ready-to-use prompt templates for common professional scenarios
            </p>
          </div>

          <div className="space-y-8">
            {useCases.map((useCase, index) => {
              const Icon = useCase.icon
              return (
                <div key={index} className="example-card card p-8">
                  <div className="flex items-start space-x-6 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {useCase.title}
                      </h3>
                      <p className="text-slate-400 leading-relaxed">
                        {useCase.description}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Prompt Template */}
                    <div className="lg:col-span-2">
                      <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                        <Lightbulb className="w-5 h-5 mr-2 text-primary-400" />
                        Prompt Template
                      </h4>
                      <CodeBlock
                        code={useCase.prompt}
                        language="text"
                        title={`${useCase.title} Template`}
                        showLineNumbers={false}
                        copyable={true}
                      />
                    </div>

                    {/* Tips */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                        <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
                        Pro Tips
                      </h4>
                      <div className="space-y-3">
                        {useCase.tips.map((tip, idx) => (
                          <div key={idx} className="flex items-start space-x-2">
                            <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-sm text-slate-300">{tip}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Next Steps */}
        <div className="text-center">
          <div className="card p-8 bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20">
            <h3 className="text-2xl font-semibold text-white mb-4">
              Ready for Advanced Techniques?
            </h3>
            <p className="body-normal mb-6 max-w-2xl mx-auto">
              Now that you've mastered the basics, let's explore cutting-edge techniques
              like Tree of Thoughts, Constitutional AI, and Multi-Modal prompting.
            </p>
            <a
              href="/section/advanced-techniques"
              className="btn btn-primary px-8 py-3 text-lg font-semibold inline-flex items-center group"
            >
              <span>Advanced Techniques</span>
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default OperatingInstructions
