{"config": {"configFile": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\playwright.config.ts", "rootDir": "E:/Augment Code Testing/ai-prompts-mastery-website/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Augment Code Testing/ai-prompts-mastery-website/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "E:/Augment Code Testing/ai-prompts-mastery-website/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/ai-prompts-mastery-website/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "E:/Augment Code Testing/ai-prompts-mastery-website/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/ai-prompts-mastery-website/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "E:/Augment Code Testing/ai-prompts-mastery-website/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/ai-prompts-mastery-website/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/Augment Code Testing/ai-prompts-mastery-website/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/ai-prompts-mastery-website/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/Augment Code Testing/ai-prompts-mastery-website/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/ai-prompts-mastery-website/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "E:/Augment Code Testing/ai-prompts-mastery-website/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/ai-prompts-mastery-website/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "E:/Augment Code Testing/ai-prompts-mastery-website/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "homepage.spec.ts", "file": "homepage.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Homepage", "file": "homepage.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should load homepage successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 10998, "error": {"message": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"text-xl font-bold gradient-text\">AI Prompts Mastery</h1> aka getByRole('link', { name: 'AI Prompts Mastery Guide Home' })\n    2) <h1 class=\"hero-title heading-1 mb-6\">…</h1> aka getByRole('heading', { name: 'AI System Prompts Mastery' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"text-xl font-bold gradient-text\">AI Prompts Mastery</h1> aka getByRole('link', { name: 'AI Prompts Mastery Guide Home' })\n    2) <h1 class=\"hero-title heading-1 mb-6\">…</h1> aka getByRole('heading', { name: 'AI System Prompts Mastery' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:13:38", "location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 38, "line": 13}, "snippet": "\u001b[0m \u001b[90m 11 |\u001b[39m     \n \u001b[90m 12 |\u001b[39m     \u001b[90m// Check main heading\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'AI System Prompts'\u001b[39m)\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 14 |\u001b[39m     \n \u001b[90m 15 |\u001b[39m     \u001b[90m// Check hero section\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.hero-title'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 38, "line": 13}, "message": "Error: expect.toContainText: Error: strict mode violation: locator('h1') resolved to 2 elements:\n    1) <h1 class=\"text-xl font-bold gradient-text\">AI Prompts Mastery</h1> aka getByRole('link', { name: 'AI Prompts Mastery Guide Home' })\n    2) <h1 class=\"hero-title heading-1 mb-6\">…</h1> aka getByRole('heading', { name: 'AI System Prompts Mastery' })\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 11 |\u001b[39m     \n \u001b[90m 12 |\u001b[39m     \u001b[90m// Check main heading\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'AI System Prompts'\u001b[39m)\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 14 |\u001b[39m     \n \u001b[90m 15 |\u001b[39m     \u001b[90m// Check hero section\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.hero-title'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:13:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:53:42.177Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-load-homepage-successfully-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-load-homepage-successfully-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-load-homepage-successfully-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 38, "line": 13}}], "status": "unexpected"}], "id": "84e7eb052e8cbae1c9f0-23065a281364f96fc99e", "file": "homepage.spec.ts", "line": 8, "column": 3}, {"title": "should have working navigation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 10615, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('text=Home') resolved to 2 elements:\n    1) <a href=\"/\" class=\"nav-link text-sm font-medium\">Home</a> aka getByRole('banner').getByRole('link', { name: 'Home', exact: true })\n    2) <span class=\"text-sm lg:text-base font-medium\">Home</span> aka getByText('HomeWelcome & Overview')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Home')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('text=Home') resolved to 2 elements:\n    1) <a href=\"/\" class=\"nav-link text-sm font-medium\">Home</a> aka getByRole('banner').getByRole('link', { name: 'Home', exact: true })\n    2) <span class=\"text-sm lg:text-base font-medium\">Home</span> aka getByText('HomeWelcome & Overview')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Home')\u001b[22m\n\n    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:38:50", "location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 50, "line": 38}, "snippet": "\u001b[0m \u001b[90m 36 |\u001b[39m     \n \u001b[90m 37 |\u001b[39m     \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m item \u001b[36mof\u001b[39m navItems) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 38 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m`text=${item}`\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 39 |\u001b[39m     }\n \u001b[90m 40 |\u001b[39m   })\n \u001b[90m 41 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 50, "line": 38}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('text=Home') resolved to 2 elements:\n    1) <a href=\"/\" class=\"nav-link text-sm font-medium\">Home</a> aka getByRole('banner').getByRole('link', { name: 'Home', exact: true })\n    2) <span class=\"text-sm lg:text-base font-medium\">Home</span> aka getByText('HomeWelcome & Overview')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Home')\u001b[22m\n\n\n\u001b[0m \u001b[90m 36 |\u001b[39m     \n \u001b[90m 37 |\u001b[39m     \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m item \u001b[36mof\u001b[39m navItems) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 38 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m`text=${item}`\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 39 |\u001b[39m     }\n \u001b[90m 40 |\u001b[39m   })\n \u001b[90m 41 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:38:50\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:53:42.145Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-navigation-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-navigation-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-navigation-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 50, "line": 38}}], "status": "unexpected"}], "id": "84e7eb052e8cbae1c9f0-1943b5afa983c2e52c69", "file": "homepage.spec.ts", "line": 21, "column": 3}, {"title": "should have responsive design", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 10651, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.container') resolved to 4 elements:\n    1) <div class=\"header-content container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('div').filter({ hasText: 'AI Prompts MasteryInteractive GuideHomeOwner\\'s ManualTechnical SpecsOperating' }).nth(2)\n    2) <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('div').filter({ hasText: 'HomeWelcome & OverviewOwner\\'s' }).nth(2)\n    3) <div class=\"relative container mx-auto px-4 sm:px-6 lg:px-8 text-center\">…</div> aka getByText('AI System PromptsMastery GuideThe Ultimate Interactive ManualTransform from')\n    4) <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('#root div').filter({ hasText: 'AI Prompts MasteryInteractive GuideThe ultimate interactive guide to mastering' }).nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.container')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('.container') resolved to 4 elements:\n    1) <div class=\"header-content container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('div').filter({ hasText: 'AI Prompts MasteryInteractive GuideHomeOwner\\'s ManualTechnical SpecsOperating' }).nth(2)\n    2) <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('div').filter({ hasText: 'HomeWelcome & OverviewOwner\\'s' }).nth(2)\n    3) <div class=\"relative container mx-auto px-4 sm:px-6 lg:px-8 text-center\">…</div> aka getByText('AI System PromptsMastery GuideThe Ultimate Interactive ManualTransform from')\n    4) <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('#root div').filter({ hasText: 'AI Prompts MasteryInteractive GuideThe ultimate interactive guide to mastering' }).nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.container')\u001b[22m\n\n    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:45:46", "location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 46, "line": 45}, "snippet": "\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Test desktop view\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.container'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Test tablet view\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m768\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m1024\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 46, "line": 45}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.container') resolved to 4 elements:\n    1) <div class=\"header-content container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('div').filter({ hasText: 'AI Prompts MasteryInteractive GuideHomeOwner\\'s ManualTechnical SpecsOperating' }).nth(2)\n    2) <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('div').filter({ hasText: 'HomeWelcome & OverviewOwner\\'s' }).nth(2)\n    3) <div class=\"relative container mx-auto px-4 sm:px-6 lg:px-8 text-center\">…</div> aka getByText('AI System PromptsMastery GuideThe Ultimate Interactive ManualTransform from')\n    4) <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">…</div> aka locator('#root div').filter({ hasText: 'AI Prompts MasteryInteractive GuideThe ultimate interactive guide to mastering' }).nth(1)\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.container')\u001b[22m\n\n\n\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Test desktop view\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.container'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Test tablet view\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m768\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m1024\u001b[39m })\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:45:46\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:53:58.474Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-responsive-design-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-responsive-design-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-responsive-design-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 46, "line": 45}}], "status": "unexpected"}], "id": "84e7eb052e8cbae1c9f0-cf3a61629c2adf4e06ff", "file": "homepage.spec.ts", "line": 42, "column": 3}, {"title": "should have working CTA buttons", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 1, "status": "failed", "duration": 13305, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveAttribute\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('text=Start Learning')\nExpected string: \u001b[32m\"/section/owners-manual\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Start Learning')\u001b[22m\n\u001b[2m    8 × locator resolved to <span>Start Learning</span>\u001b[22m\n\u001b[2m      - unexpected value \"null\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveAttribute\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('text=Start Learning')\nExpected string: \u001b[32m\"/section/owners-manual\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Start Learning')\u001b[22m\n\u001b[2m    8 × locator resolved to <span>Start Learning</span>\u001b[22m\n\u001b[2m      - unexpected value \"null\"\u001b[22m\n\n    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:63:31", "location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 31, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[36mconst\u001b[39m startButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Start Learning'\u001b[39m)\n \u001b[90m 62 |\u001b[39m     \u001b[36mawait\u001b[39m expect(startButton)\u001b[33m.\u001b[39mtoBeVisible()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(startButton)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'/section/owners-manual'\u001b[39m)\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check \"Advanced Techniques\" button\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mconst\u001b[39m advancedButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Advanced Techniques'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 31, "line": 63}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveAttribute\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('text=Start Learning')\nExpected string: \u001b[32m\"/section/owners-manual\"\u001b[39m\nReceived string: \u001b[31m\"\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveAttribute\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Start Learning')\u001b[22m\n\u001b[2m    8 × locator resolved to <span>Start Learning</span>\u001b[22m\n\u001b[2m      - unexpected value \"null\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \u001b[36mconst\u001b[39m startButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Start Learning'\u001b[39m)\n \u001b[90m 62 |\u001b[39m     \u001b[36mawait\u001b[39m expect(startButton)\u001b[33m.\u001b[39mtoBeVisible()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m expect(startButton)\u001b[33m.\u001b[39mtoHaveAttribute(\u001b[32m'href'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'/section/owners-manual'\u001b[39m)\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Check \"Advanced Techniques\" button\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mconst\u001b[39m advancedButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Advanced Techniques'\u001b[39m)\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:63:31\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:53:58.682Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-CTA-buttons-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-CTA-buttons-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-CTA-buttons-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 31, "line": 63}}], "status": "unexpected"}], "id": "84e7eb052e8cbae1c9f0-d7e8967b6b81009d5859", "file": "homepage.spec.ts", "line": 59, "column": 3}, {"title": "should have features section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 13106, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:54:17.460Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "84e7eb052e8cbae1c9f0-642c0fb473e9588c5920", "file": "homepage.spec.ts", "line": 71, "column": 3}, {"title": "should have guide sections", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 10102, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:54:17.553Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "84e7eb052e8cbae1c9f0-0b2e929b484392a0a249", "file": "homepage.spec.ts", "line": 88, "column": 3}, {"title": "should have working scroll progress", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 8567, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.h-full.bg-gradient-to-r') resolved to 2 elements:\n    1) <div class=\"h-full bg-gradient-to-r from-primary-500 to-accent-500 transition-all duration-300\"></div> aka locator('.h-full').first()\n    2) <div class=\"h-full bg-gradient-to-r from-primary-500 via-accent-500 to-primary-600 transition-all duration-150 ease-out\"></div> aka locator('.h-full.bg-gradient-to-r.from-primary-500.via-accent-500')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.h-full.bg-gradient-to-r')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('.h-full.bg-gradient-to-r') resolved to 2 elements:\n    1) <div class=\"h-full bg-gradient-to-r from-primary-500 to-accent-500 transition-all duration-300\"></div> aka locator('.h-full').first()\n    2) <div class=\"h-full bg-gradient-to-r from-primary-500 via-accent-500 to-primary-600 transition-all duration-150 ease-out\"></div> aka locator('.h-full.bg-gradient-to-r.from-primary-500.via-accent-500')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.h-full.bg-gradient-to-r')\u001b[22m\n\n    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:118:31", "location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 31, "line": 118}, "snippet": "\u001b[0m \u001b[90m 116 |\u001b[39m     \u001b[90m// Check that progress bar has some width\u001b[39m\n \u001b[90m 117 |\u001b[39m     \u001b[36mconst\u001b[39m progressBar \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.h-full.bg-gradient-to-r'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 118 |\u001b[39m     \u001b[36mawait\u001b[39m expect(progressBar)\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 119 |\u001b[39m   })\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m   test(\u001b[32m'should have accessibility features'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 31, "line": 118}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.h-full.bg-gradient-to-r') resolved to 2 elements:\n    1) <div class=\"h-full bg-gradient-to-r from-primary-500 to-accent-500 transition-all duration-300\"></div> aka locator('.h-full').first()\n    2) <div class=\"h-full bg-gradient-to-r from-primary-500 via-accent-500 to-primary-600 transition-all duration-150 ease-out\"></div> aka locator('.h-full.bg-gradient-to-r.from-primary-500.via-accent-500')\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.h-full.bg-gradient-to-r')\u001b[22m\n\n\n\u001b[0m \u001b[90m 116 |\u001b[39m     \u001b[90m// Check that progress bar has some width\u001b[39m\n \u001b[90m 117 |\u001b[39m     \u001b[36mconst\u001b[39m progressBar \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.h-full.bg-gradient-to-r'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 118 |\u001b[39m     \u001b[36mawait\u001b[39m expect(progressBar)\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 119 |\u001b[39m   })\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m   test(\u001b[32m'should have accessibility features'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:118:31\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:54:28.374Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-scroll-progress-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-scroll-progress-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-working-scroll-progress-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 31, "line": 118}}], "status": "unexpected"}], "id": "84e7eb052e8cbae1c9f0-9080bfac06e8060d63bd", "file": "homepage.spec.ts", "line": 108, "column": 3}, {"title": "should have accessibility features", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "failed", "duration": 13262, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeInViewport\u001b[2m()\u001b[22m\n\nLocator: locator('text=Skip to main content')\nExpected: in viewport\nReceived: viewport ratio 0\nCall log:\n\u001b[2m  - Expect \"toBeInViewport\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Skip to main content')\u001b[22m\n\u001b[2m    8 × locator resolved to <a href=\"#main-content\" class=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-primary-600 text-white px-4 py-2 rounded-lg\">Skip to main content</a>\u001b[22m\n\u001b[2m      - unexpected value \"viewport ratio 0\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeInViewport\u001b[2m()\u001b[22m\n\nLocator: locator('text=Skip to main content')\nExpected: in viewport\nReceived: viewport ratio 0\nCall log:\n\u001b[2m  - Expect \"toBeInViewport\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Skip to main content')\u001b[22m\n\u001b[2m    8 × locator resolved to <a href=\"#main-content\" class=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-primary-600 text-white px-4 py-2 rounded-lg\">Skip to main content</a>\u001b[22m\n\u001b[2m      - unexpected value \"viewport ratio 0\"\u001b[22m\n\n    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:123:61", "location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 61, "line": 123}, "snippet": "\u001b[0m \u001b[90m 121 |\u001b[39m   test(\u001b[32m'should have accessibility features'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 122 |\u001b[39m     \u001b[90m// Check skip link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 123 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Skip to main content'\u001b[39m))\u001b[33m.\u001b[39mtoBeInViewport({ ratio\u001b[33m:\u001b[39m \u001b[35m0\u001b[39m })\n \u001b[90m     |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 124 |\u001b[39m     \n \u001b[90m 125 |\u001b[39m     \u001b[90m// Check main content has proper ID\u001b[39m\n \u001b[90m 126 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#main-content'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 61, "line": 123}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeInViewport\u001b[2m()\u001b[22m\n\nLocator: locator('text=Skip to main content')\nExpected: in viewport\nReceived: viewport ratio 0\nCall log:\n\u001b[2m  - Expect \"toBeInViewport\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Skip to main content')\u001b[22m\n\u001b[2m    8 × locator resolved to <a href=\"#main-content\" class=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-primary-600 text-white px-4 py-2 rounded-lg\">Skip to main content</a>\u001b[22m\n\u001b[2m      - unexpected value \"viewport ratio 0\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 121 |\u001b[39m   test(\u001b[32m'should have accessibility features'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 122 |\u001b[39m     \u001b[90m// Check skip link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 123 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Skip to main content'\u001b[39m))\u001b[33m.\u001b[39mtoBeInViewport({ ratio\u001b[33m:\u001b[39m \u001b[35m0\u001b[39m })\n \u001b[90m     |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 124 |\u001b[39m     \n \u001b[90m 125 |\u001b[39m     \u001b[90m// Check main content has proper ID\u001b[39m\n \u001b[90m 126 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#main-content'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:123:61\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:54:31.069Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-accessibility-features-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-accessibility-features-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-have-accessibility-features-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 61, "line": 123}}], "status": "unexpected"}], "id": "84e7eb052e8cbae1c9f0-ea5bdacbb6ff8af43a8f", "file": "homepage.spec.ts", "line": 121, "column": 3}, {"title": "should handle mobile menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "interrupted", "duration": 7480, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('text=Owner\\'s Manual') resolved to 5 elements:\n    1) <a href=\"/section/owners-manual\" class=\"nav-link text-sm font-medium\">Owner's Manual</a> aka getByText('Owner\\'s Manual').first()\n    2) <a href=\"/section/owners-manual\" class=\"block px-4 py-2 text-slate-300 hover:text-white hover:bg-slate-800/50 rounded-lg transition-colors\">Owner's Manual</a> aka getByRole('banner').getByRole('link', { name: 'Owner\\'s Manual' })\n    3) <span class=\"text-sm lg:text-base font-medium\">Owner's Manual</span> aka getByText('Owner\\'s ManualBasic Understanding & Navigation')\n    4) <h3 class=\"text-xl font-semibold text-white mb-2 group-hover:text-primary-400 transition-colors duration-200\">Owner's Manual</h3> aka getByRole('link', { name: 'Owner\\'s Manual Basic' })\n    5) <a href=\"/section/owners-manual\" class=\"text-slate-400 hover:text-white transition-colors duration-200 text-sm\">Owner's Manual</a> aka getByRole('contentinfo').getByRole('link', { name: 'Owner\\'s Manual' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Owner\\'s Manual')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('text=Owner\\'s Manual') resolved to 5 elements:\n    1) <a href=\"/section/owners-manual\" class=\"nav-link text-sm font-medium\">Owner's Manual</a> aka getByText('Owner\\'s Manual').first()\n    2) <a href=\"/section/owners-manual\" class=\"block px-4 py-2 text-slate-300 hover:text-white hover:bg-slate-800/50 rounded-lg transition-colors\">Owner's Manual</a> aka getByRole('banner').getByRole('link', { name: 'Owner\\'s Manual' })\n    3) <span class=\"text-sm lg:text-base font-medium\">Owner's Manual</span> aka getByText('Owner\\'s ManualBasic Understanding & Navigation')\n    4) <h3 class=\"text-xl font-semibold text-white mb-2 group-hover:text-primary-400 transition-colors duration-200\">Owner's Manual</h3> aka getByRole('link', { name: 'Owner\\'s Manual Basic' })\n    5) <a href=\"/section/owners-manual\" class=\"text-slate-400 hover:text-white transition-colors duration-200 text-sm\">Owner's Manual</a> aka getByRole('contentinfo').getByRole('link', { name: 'Owner\\'s Manual' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Owner\\'s Manual')\u001b[22m\n\n    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:150:56", "location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 56, "line": 150}, "snippet": "\u001b[0m \u001b[90m 148 |\u001b[39m     \n \u001b[90m 149 |\u001b[39m     \u001b[90m// Check navigation items are visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 150 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Owner\\'s Manual'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 151 |\u001b[39m     \n \u001b[90m 152 |\u001b[39m     \u001b[90m// Click to close menu\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mawait\u001b[39m menuButton\u001b[33m.\u001b[39mclick()\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 56, "line": 150}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('text=Owner\\'s Manual') resolved to 5 elements:\n    1) <a href=\"/section/owners-manual\" class=\"nav-link text-sm font-medium\">Owner's Manual</a> aka getByText('Owner\\'s Manual').first()\n    2) <a href=\"/section/owners-manual\" class=\"block px-4 py-2 text-slate-300 hover:text-white hover:bg-slate-800/50 rounded-lg transition-colors\">Owner's Manual</a> aka getByRole('banner').getByRole('link', { name: 'Owner\\'s Manual' })\n    3) <span class=\"text-sm lg:text-base font-medium\">Owner's Manual</span> aka getByText('Owner\\'s ManualBasic Understanding & Navigation')\n    4) <h3 class=\"text-xl font-semibold text-white mb-2 group-hover:text-primary-400 transition-colors duration-200\">Owner's Manual</h3> aka getByRole('link', { name: 'Owner\\'s Manual Basic' })\n    5) <a href=\"/section/owners-manual\" class=\"text-slate-400 hover:text-white transition-colors duration-200 text-sm\">Owner's Manual</a> aka getByRole('contentinfo').getByRole('link', { name: 'Owner\\'s Manual' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Owner\\'s Manual')\u001b[22m\n\n\n\u001b[0m \u001b[90m 148 |\u001b[39m     \n \u001b[90m 149 |\u001b[39m     \u001b[90m// Check navigation items are visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 150 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Owner\\'s Manual'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 151 |\u001b[39m     \n \u001b[90m 152 |\u001b[39m     \u001b[90m// Click to close menu\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mawait\u001b[39m menuButton\u001b[33m.\u001b[39mclick()\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts:150:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:54:41.585Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-handle-mobile-menu-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-handle-mobile-menu-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\test-results\\homepage-Homepage-should-handle-mobile-menu-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\ai-prompts-mastery-website\\tests\\homepage.spec.ts", "column": 56, "line": 150}}], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-8d2dc313d6ed19b9281f", "file": "homepage.spec.ts", "line": 135, "column": 3}, {"title": "should load homepage successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "skipped", "duration": 0, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T03:54:50.357Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-f742d5c55baa453c1e50", "file": "homepage.spec.ts", "line": 8, "column": 3}, {"title": "should have working navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-7fa807c0deb2e74be0c3", "file": "homepage.spec.ts", "line": 21, "column": 3}, {"title": "should have responsive design", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-84d3d6ff05d375ec99d7", "file": "homepage.spec.ts", "line": 42, "column": 3}, {"title": "should have working CTA buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-0a16a9ee537bb0807261", "file": "homepage.spec.ts", "line": 59, "column": 3}, {"title": "should have features section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-6df3075766827a651cf6", "file": "homepage.spec.ts", "line": 71, "column": 3}, {"title": "should have guide sections", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-33da3abd3d356203b8f3", "file": "homepage.spec.ts", "line": 88, "column": 3}, {"title": "should have working scroll progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-883cf8fb24b603a7b598", "file": "homepage.spec.ts", "line": 108, "column": 3}, {"title": "should have accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-70c259f7bc74e7b56a59", "file": "homepage.spec.ts", "line": 121, "column": 3}, {"title": "should handle mobile menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-cb029788ce475acef404", "file": "homepage.spec.ts", "line": 135, "column": 3}, {"title": "should load homepage successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-f79b8dcc2a8b85697b43", "file": "homepage.spec.ts", "line": 8, "column": 3}, {"title": "should have working navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-263d4e50b22dafeb8508", "file": "homepage.spec.ts", "line": 21, "column": 3}, {"title": "should have responsive design", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-1b1f6e211fb42a8aab2e", "file": "homepage.spec.ts", "line": 42, "column": 3}, {"title": "should have working CTA buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-3c894d8b0e44144573f8", "file": "homepage.spec.ts", "line": 59, "column": 3}, {"title": "should have features section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-b6b63aa9bf9fc2e9b68b", "file": "homepage.spec.ts", "line": 71, "column": 3}, {"title": "should have guide sections", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-065a31849c7853158d0d", "file": "homepage.spec.ts", "line": 88, "column": 3}, {"title": "should have working scroll progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-1c92ba2e38ee53f7b813", "file": "homepage.spec.ts", "line": 108, "column": 3}, {"title": "should have accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-40c9036fa086edcfb97a", "file": "homepage.spec.ts", "line": 121, "column": 3}, {"title": "should handle mobile menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-588d3eea05941f161779", "file": "homepage.spec.ts", "line": 135, "column": 3}, {"title": "should load homepage successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-960b69da7337b5660dbb", "file": "homepage.spec.ts", "line": 8, "column": 3}, {"title": "should have working navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-6ff973f112b7b0239447", "file": "homepage.spec.ts", "line": 21, "column": 3}, {"title": "should have responsive design", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-e6d6b56962eb102ab2b6", "file": "homepage.spec.ts", "line": 42, "column": 3}, {"title": "should have working CTA buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-e4047f83c5f5562346f7", "file": "homepage.spec.ts", "line": 59, "column": 3}, {"title": "should have features section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-beb087cb8fb09844376c", "file": "homepage.spec.ts", "line": 71, "column": 3}, {"title": "should have guide sections", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-973abd0d7f6c16d771ab", "file": "homepage.spec.ts", "line": 88, "column": 3}, {"title": "should have working scroll progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-7c54270a018ed4fd48a7", "file": "homepage.spec.ts", "line": 108, "column": 3}, {"title": "should have accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-0f834af3610de246874a", "file": "homepage.spec.ts", "line": 121, "column": 3}, {"title": "should handle mobile menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-a50cf976caeaf969c8fd", "file": "homepage.spec.ts", "line": 135, "column": 3}, {"title": "should load homepage successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-3c0bb6a41c79bbf800b4", "file": "homepage.spec.ts", "line": 8, "column": 3}, {"title": "should have working navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-90f3c63153eaf0a16a64", "file": "homepage.spec.ts", "line": 21, "column": 3}, {"title": "should have responsive design", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-f103ecb8016e16304cd9", "file": "homepage.spec.ts", "line": 42, "column": 3}, {"title": "should have working CTA buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-c41afd6d069c0b8c5ed0", "file": "homepage.spec.ts", "line": 59, "column": 3}, {"title": "should have features section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-02860311bd0d244a2598", "file": "homepage.spec.ts", "line": 71, "column": 3}, {"title": "should have guide sections", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-a33630319876b77b91d6", "file": "homepage.spec.ts", "line": 88, "column": 3}, {"title": "should have working scroll progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-f996576efab034e56b86", "file": "homepage.spec.ts", "line": 108, "column": 3}, {"title": "should have accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-b983de925cc6764cff9b", "file": "homepage.spec.ts", "line": 121, "column": 3}, {"title": "should handle mobile menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-c3a52c14de6a7e88c2e7", "file": "homepage.spec.ts", "line": 135, "column": 3}, {"title": "should load homepage successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-30a6ecad1a852189d57a", "file": "homepage.spec.ts", "line": 8, "column": 3}, {"title": "should have working navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-88570b7c94b19fdc16ad", "file": "homepage.spec.ts", "line": 21, "column": 3}, {"title": "should have responsive design", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-912d1e4697e396b0aee1", "file": "homepage.spec.ts", "line": 42, "column": 3}, {"title": "should have working CTA buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-4334343bf8766c34f5af", "file": "homepage.spec.ts", "line": 59, "column": 3}, {"title": "should have features section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-11091a3359047216aa7b", "file": "homepage.spec.ts", "line": 71, "column": 3}, {"title": "should have guide sections", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-89f4942b92c1513e9a86", "file": "homepage.spec.ts", "line": 88, "column": 3}, {"title": "should have working scroll progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-9a563e605e4b456e8d81", "file": "homepage.spec.ts", "line": 108, "column": 3}, {"title": "should have accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-178074fb83bcb6407e16", "file": "homepage.spec.ts", "line": 121, "column": 3}, {"title": "should handle mobile menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-8c72feb74a1b0cf92776", "file": "homepage.spec.ts", "line": 135, "column": 3}, {"title": "should load homepage successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-4bd2eb53ff16d483ad6d", "file": "homepage.spec.ts", "line": 8, "column": 3}, {"title": "should have working navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-5359458f19bef1e868bf", "file": "homepage.spec.ts", "line": 21, "column": 3}, {"title": "should have responsive design", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-1e8a90bf4d2a11537f75", "file": "homepage.spec.ts", "line": 42, "column": 3}, {"title": "should have working CTA buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-ff884fe27650b01946d9", "file": "homepage.spec.ts", "line": 59, "column": 3}, {"title": "should have features section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-7b458d81c3ae8e233866", "file": "homepage.spec.ts", "line": 71, "column": 3}, {"title": "should have guide sections", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-708d751d1786b50fe578", "file": "homepage.spec.ts", "line": 88, "column": 3}, {"title": "should have working scroll progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-f9c615c2a5cea04bde63", "file": "homepage.spec.ts", "line": 108, "column": 3}, {"title": "should have accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-6728ace37c2acea4df8b", "file": "homepage.spec.ts", "line": 121, "column": 3}, {"title": "should handle mobile menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "84e7eb052e8cbae1c9f0-eadcd088e162881ab5a1", "file": "homepage.spec.ts", "line": 135, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-26T03:53:39.590Z", "duration": 70806.922, "expected": 2, "skipped": 55, "unexpected": 6, "flaky": 0}}